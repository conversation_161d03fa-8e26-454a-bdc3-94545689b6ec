// app.js
App({
  globalData: {
    userInfo: null,
    cart: [],
    totalPrice: 0,
    deliveryFee: 10, // 配送费
    serviceRate: 0.05 // 服务费率 5%
  },

  onLaunch() {
    // 初始化云开发
    if (!wx.cloud) {
      console.error('请使用 2.2.3 或以上的基础库以使用云能力')
    } else {
      wx.cloud.init({
        env: 'your-env-id', // 请替换为您的云开发环境ID
        traceUser: true,
      })
    }

    // 获取用户信息
    this.getUserInfo()
    
    // 从本地存储加载购物车
    this.loadCartFromStorage()
  },

  getUserInfo() {
    wx.getSetting({
      success: res => {
        if (res.authSetting['scope.userInfo']) {
          wx.getUserInfo({
            success: res => {
              this.globalData.userInfo = res.userInfo
            }
          })
        }
      }
    })
  },

  // 加载购物车数据
  loadCartFromStorage() {
    try {
      const cart = wx.getStorageSync('cart')
      if (cart) {
        this.globalData.cart = cart
        this.calculateTotalPrice()
      }
    } catch (e) {
      console.error('加载购物车失败', e)
    }
  },

  // 保存购物车到本地存储
  saveCartToStorage() {
    try {
      wx.setStorageSync('cart', this.globalData.cart)
    } catch (e) {
      console.error('保存购物车失败', e)
    }
  },

  // 添加商品到购物车
  addToCart(product) {
    const existingItem = this.globalData.cart.find(item => item.id === product.id)
    
    if (existingItem) {
      existingItem.quantity += 1
    } else {
      this.globalData.cart.push({
        ...product,
        quantity: 1
      })
    }
    
    this.calculateTotalPrice()
    this.saveCartToStorage()
    
    wx.showToast({
      title: '已添加到购物车',
      icon: 'success'
    })
  },

  // 更新购物车商品数量
  updateCartQuantity(productId, quantity) {
    const item = this.globalData.cart.find(item => item.id === productId)
    if (item) {
      if (quantity <= 0) {
        this.removeFromCart(productId)
      } else {
        item.quantity = quantity
        this.calculateTotalPrice()
        this.saveCartToStorage()
      }
    }
  },

  // 从购物车移除商品
  removeFromCart(productId) {
    this.globalData.cart = this.globalData.cart.filter(item => item.id !== productId)
    this.calculateTotalPrice()
    this.saveCartToStorage()
  },

  // 计算总价
  calculateTotalPrice() {
    const subtotal = this.globalData.cart.reduce((total, item) => {
      return total + (item.price * item.quantity)
    }, 0)
    
    const serviceFee = subtotal * this.globalData.serviceRate
    this.globalData.totalPrice = subtotal + serviceFee + this.globalData.deliveryFee
  },

  // 清空购物车
  clearCart() {
    this.globalData.cart = []
    this.globalData.totalPrice = 0
    this.saveCartToStorage()
  }
})
