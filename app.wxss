/* app.wxss */
page {
  background-color: #f8f8f8;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Helvetica Neue', Arial, sans-serif;
}

/* 通用样式 */
.container {
  padding: 20rpx;
}

.card {
  background: white;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 20rpx;
  overflow: hidden;
}

.btn-primary {
  background: #1AAD19;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 20rpx 40rpx;
  font-size: 32rpx;
}

.btn-secondary {
  background: #f0f0f0;
  color: #333;
  border: none;
  border-radius: 8rpx;
  padding: 20rpx 40rpx;
  font-size: 32rpx;
}

.text-primary {
  color: #1AAD19;
}

.text-secondary {
  color: #666;
}

.text-danger {
  color: #e74c3c;
}

.text-center {
  text-align: center;
}

.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.flex-between {
  justify-content: space-between;
}

.flex-center {
  justify-content: center;
  align-items: center;
}

.flex-1 {
  flex: 1;
}

.mt-10 {
  margin-top: 20rpx;
}

.mt-20 {
  margin-top: 40rpx;
}

.mb-10 {
  margin-bottom: 20rpx;
}

.mb-20 {
  margin-bottom: 40rpx;
}

.p-10 {
  padding: 20rpx;
}

.p-20 {
  padding: 40rpx;
}

/* 商品卡片样式 */
.product-card {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.product-image {
  width: 100%;
  height: 300rpx;
  object-fit: cover;
}

.product-info {
  padding: 20rpx;
}

.product-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 10rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-desc {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 15rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.product-price {
  font-size: 36rpx;
  font-weight: 600;
  color: #e74c3c;
}

.product-original-price {
  font-size: 28rpx;
  color: #999;
  text-decoration: line-through;
  margin-left: 10rpx;
}

/* 数量选择器 */
.quantity-selector {
  display: flex;
  align-items: center;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  overflow: hidden;
}

.quantity-btn {
  width: 60rpx;
  height: 60rpx;
  background: #f8f8f8;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #666;
}

.quantity-btn:active {
  background: #e8e8e8;
}

.quantity-input {
  width: 80rpx;
  height: 60rpx;
  text-align: center;
  border: none;
  font-size: 28rpx;
  background: white;
}

/* 购物车徽章 */
.cart-badge {
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  background: #e74c3c;
  color: white;
  border-radius: 50%;
  width: 40rpx;
  height: 40rpx;
  font-size: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 40rpx;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 100rpx 40rpx;
  color: #999;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 32rpx;
  margin-bottom: 40rpx;
}
