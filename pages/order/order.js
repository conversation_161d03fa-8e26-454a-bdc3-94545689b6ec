// pages/order/order.js
const app = getApp()

Page({
  data: {
    orderType: 'cart', // cart: 购物车结算, buy: 立即购买
    orderItems: [],
    selectedAddress: null,
    deliveryType: 'standard',
    selectedTimeSlot: null,
    timeSlots: [],
    orderNote: '',
    selectedCoupon: null,
    availableCoupons: [],
    subtotal: 0,
    serviceFee: 0,
    deliveryFee: 10,
    couponDiscount: 0,
    totalPrice: 0
  },

  onLoad(options) {
    const orderType = options.type || 'cart'
    this.setData({ orderType })

    if (orderType === 'cart') {
      // 从购物车结算
      const items = JSON.parse(decodeURIComponent(options.items || '[]'))
      this.setData({ orderItems: items })
    } else if (orderType === 'buy') {
      // 立即购买
      const product = JSON.parse(decodeURIComponent(options.product || '{}'))
      this.setData({ orderItems: [product] })
    }

    this.loadUserAddress()
    this.loadTimeSlots()
    this.loadCoupons()
    this.calculatePrice()
  },

  loadUserAddress() {
    // 模拟用户地址数据
    const mockAddress = {
      id: 1,
      name: '张三',
      phone: '138****8888',
      address: '北京市朝阳区三里屯街道工体北路8号院1号楼101室',
      isDefault: true
    }
    
    this.setData({
      selectedAddress: mockAddress
    })
  },

  loadTimeSlots() {
    const now = new Date()
    const timeSlots = []
    
    // 生成今天和明天的时间段
    for (let day = 0; day < 2; day++) {
      const date = new Date(now.getTime() + day * 24 * 60 * 60 * 1000)
      const dateStr = day === 0 ? '今天' : '明天'
      
      // 每天4个时间段
      const slots = [
        { start: '09:00', end: '12:00', label: '上午' },
        { start: '12:00', end: '15:00', label: '中午' },
        { start: '15:00', end: '18:00', label: '下午' },
        { start: '18:00', end: '21:00', label: '晚上' }
      ]
      
      slots.forEach((slot, index) => {
        timeSlots.push({
          id: day * 10 + index,
          label: `${dateStr} ${slot.label} ${slot.start}-${slot.end}`,
          date: date.toDateString(),
          start: slot.start,
          end: slot.end
        })
      })
    }
    
    this.setData({
      timeSlots: timeSlots,
      selectedTimeSlot: timeSlots[0].id
    })
  },

  loadCoupons() {
    // 模拟优惠券数据
    const coupons = [
      {
        id: 1,
        name: '新用户专享券',
        discount: 10,
        minAmount: 50,
        type: 'amount'
      },
      {
        id: 2,
        name: '满100减20',
        discount: 20,
        minAmount: 100,
        type: 'amount'
      }
    ]
    
    // 筛选可用优惠券
    const availableCoupons = coupons.filter(coupon => {
      return this.data.subtotal >= coupon.minAmount
    })
    
    this.setData({
      availableCoupons: availableCoupons
    })
  },

  calculatePrice() {
    const { orderItems, deliveryType, selectedCoupon } = this.data
    
    // 计算商品总价
    const subtotal = orderItems.reduce((total, item) => {
      return total + (item.price * item.quantity)
    }, 0)
    
    // 计算服务费
    const serviceFee = (subtotal * app.globalData.serviceRate).toFixed(2)
    
    // 计算配送费
    let deliveryFee = app.globalData.deliveryFee
    if (deliveryType === 'express') {
      deliveryFee += 15 // 急速配送加收15元
    }
    
    // 计算优惠券折扣
    const couponDiscount = selectedCoupon ? selectedCoupon.discount : 0
    
    // 计算总价
    const totalPrice = Math.max(0, subtotal + parseFloat(serviceFee) + deliveryFee - couponDiscount).toFixed(2)
    
    this.setData({
      subtotal: subtotal.toFixed(2),
      serviceFee: serviceFee,
      deliveryFee: deliveryFee,
      couponDiscount: couponDiscount,
      totalPrice: totalPrice
    })
  },

  onAddressSelect() {
    wx.navigateTo({
      url: '/pages/address/address?mode=select'
    })
  },

  onDeliveryTypeChange(e) {
    const deliveryType = e.currentTarget.dataset.type
    this.setData({ deliveryType })
    this.calculatePrice()
  },

  onTimeSlotSelect(e) {
    const timeSlotId = e.currentTarget.dataset.id
    this.setData({ selectedTimeSlot: timeSlotId })
  },

  onNoteInput(e) {
    this.setData({
      orderNote: e.detail.value
    })
  },

  onCouponSelect() {
    if (this.data.availableCoupons.length === 0) {
      wx.showToast({
        title: '暂无可用优惠券',
        icon: 'none'
      })
      return
    }
    
    const couponList = this.data.availableCoupons.map(coupon => 
      `${coupon.name} -¥${coupon.discount}`
    )
    
    wx.showActionSheet({
      itemList: ['不使用优惠券', ...couponList],
      success: (res) => {
        if (res.tapIndex === 0) {
          this.setData({ selectedCoupon: null })
        } else {
          const selectedCoupon = this.data.availableCoupons[res.tapIndex - 1]
          this.setData({ selectedCoupon })
        }
        this.calculatePrice()
      }
    })
  },

  onSubmitOrder() {
    const { selectedAddress, orderItems, selectedTimeSlot } = this.data
    
    if (!selectedAddress) {
      wx.showToast({
        title: '请选择收货地址',
        icon: 'none'
      })
      return
    }
    
    if (!selectedTimeSlot) {
      wx.showToast({
        title: '请选择配送时间',
        icon: 'none'
      })
      return
    }
    
    wx.showLoading({
      title: '提交订单中...'
    })
    
    // 模拟提交订单
    setTimeout(() => {
      wx.hideLoading()
      
      // 清空购物车（如果是购物车结算）
      if (this.data.orderType === 'cart') {
        app.clearCart()
      }
      
      const orderId = 'ORDER' + Date.now()
      
      wx.showToast({
        title: '订单提交成功',
        icon: 'success',
        duration: 2000,
        success: () => {
          setTimeout(() => {
            wx.redirectTo({
              url: `/pages/order-detail/order-detail?orderId=${orderId}`
            })
          }, 2000)
        }
      })
    }, 2000)
  },

  onShareAppMessage() {
    return {
      title: '山姆代购订单',
      path: '/pages/index/index'
    }
  }
})
