/* pages/order/order.wxss */

.container {
  padding-bottom: 300rpx; /* 为底部结算栏留出空间 */
}

/* 地址选择 */
.address-section {
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.address-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.address-title {
  flex: 1;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-left: 15rpx;
}

.address-content {
  margin-left: 31rpx;
}

.address-info {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-bottom: 10rpx;
}

.receiver-name {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
}

.receiver-phone {
  font-size: 28rpx;
  color: #666;
}

.address-detail {
  font-size: 28rpx;
  color: #666;
  line-height: 1.4;
}

.no-address {
  margin-left: 31rpx;
}

.no-address-text {
  font-size: 28rpx;
  color: #999;
}

/* 配送时间 */
.delivery-section {
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-left: 15rpx;
  flex: 1;
}

.product-count {
  font-size: 24rpx;
  color: #666;
}

.delivery-options {
  margin-bottom: 30rpx;
}

.delivery-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  border: 2rpx solid #eee;
  border-radius: 12rpx;
  margin-bottom: 15rpx;
}

.delivery-option.selected {
  border-color: #1AAD19;
  background: #f0f9ff;
}

.option-info {
  flex: 1;
}

.option-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
}

.option-desc {
  font-size: 24rpx;
  color: #666;
}

.option-price {
  font-size: 28rpx;
  color: #1AAD19;
  font-weight: 600;
}

.time-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 15rpx;
  display: block;
}

.time-list {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15rpx;
}

.time-slot {
  padding: 20rpx;
  border: 2rpx solid #eee;
  border-radius: 12rpx;
  text-align: center;
  font-size: 24rpx;
  color: #666;
}

.time-slot.selected {
  border-color: #1AAD19;
  background: #f0f9ff;
  color: #1AAD19;
}

/* 商品信息 */
.products-section {
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.product-list {
  margin-top: 20rpx;
}

.product-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.product-item:last-child {
  border-bottom: none;
}

.product-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
  object-fit: cover;
}

.product-info {
  flex: 1;
}

.product-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.product-spec {
  font-size: 24rpx;
  color: #666;
  background: #f0f0f0;
  padding: 6rpx 12rpx;
  border-radius: 6rpx;
  margin-bottom: 15rpx;
  display: inline-block;
}

.product-price-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.product-price {
  font-size: 28rpx;
  color: #e74c3c;
  font-weight: 600;
}

.product-quantity {
  font-size: 28rpx;
  color: #666;
}

/* 代购说明 */
.service-section {
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.service-info {
  margin: 20rpx 0;
}

.service-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
}

.service-label {
  font-size: 28rpx;
  color: #666;
}

.service-value {
  font-size: 28rpx;
  color: #333;
}

.service-note {
  background: #fff7e6;
  padding: 20rpx;
  border-radius: 12rpx;
  border-left: 4rpx solid #ffa940;
}

.note-text {
  font-size: 24rpx;
  color: #d48806;
  line-height: 1.4;
}

/* 备注信息 */
.note-section {
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.note-input {
  width: 100%;
  min-height: 120rpx;
  background: #f8f8f8;
  border-radius: 12rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333;
  margin-top: 20rpx;
  box-sizing: border-box;
}

/* 优惠券 */
.coupon-section {
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.coupon-header {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}

.coupon-title {
  flex: 1;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-left: 15rpx;
}

.coupon-content {
  margin-left: 31rpx;
}

.coupon-text {
  font-size: 28rpx;
  color: #666;
}

/* 底部结算栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 30rpx;
  border-top: 1rpx solid #eee;
  z-index: 100;
}

.price-summary {
  margin-bottom: 30rpx;
}

.price-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.price-row.total-row {
  padding-top: 15rpx;
  border-top: 1rpx solid #eee;
  margin-bottom: 0;
}

.price-label {
  font-size: 28rpx;
  color: #666;
}

.price-value {
  font-size: 28rpx;
  color: #333;
}

.price-value.discount {
  color: #1AAD19;
}

.total-price {
  font-size: 36rpx;
  font-weight: 600;
  color: #e74c3c;
}

.submit-btn {
  width: 100%;
  background: #1AAD19;
  color: white;
  border: none;
  border-radius: 50rpx;
  padding: 25rpx;
  font-size: 32rpx;
  font-weight: 600;
}

.submit-btn:disabled {
  background: #ccc;
}
