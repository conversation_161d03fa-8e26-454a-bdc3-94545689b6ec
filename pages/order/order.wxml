<!--pages/order/order.wxml-->
<view class="container">
  <!-- 收货地址 -->
  <view class="address-section card" bindtap="onAddressSelect">
    <view class="address-header">
      <icon type="location" size="16" color="#1AAD19"></icon>
      <text class="address-title">收货地址</text>
      <icon type="arrow" size="16" color="#ccc"></icon>
    </view>
    
    <view class="address-content" wx:if="{{selectedAddress}}">
      <view class="address-info">
        <text class="receiver-name">{{selectedAddress.name}}</text>
        <text class="receiver-phone">{{selectedAddress.phone}}</text>
      </view>
      <text class="address-detail">{{selectedAddress.address}}</text>
    </view>
    
    <view class="no-address" wx:else>
      <text class="no-address-text">请选择收货地址</text>
    </view>
  </view>

  <!-- 配送时间 -->
  <view class="delivery-section card">
    <view class="section-header">
      <icon type="time" size="16" color="#1AAD19"></icon>
      <text class="section-title">配送时间</text>
    </view>
    
    <view class="delivery-options">
      <view class="delivery-option {{deliveryType === 'standard' ? 'selected' : ''}}" 
            bindtap="onDeliveryTypeChange" 
            data-type="standard">
        <view class="option-info">
          <text class="option-title">标准配送</text>
          <text class="option-desc">预计2-3小时送达</text>
        </view>
        <text class="option-price">免费</text>
      </view>
      
      <view class="delivery-option {{deliveryType === 'express' ? 'selected' : ''}}" 
            bindtap="onDeliveryTypeChange" 
            data-type="express">
        <view class="option-info">
          <text class="option-title">急速配送</text>
          <text class="option-desc">预计1小时内送达</text>
        </view>
        <text class="option-price">+¥15</text>
      </view>
    </view>
    
    <view class="time-slots" wx:if="{{timeSlots.length > 0}}">
      <text class="time-title">选择时间段</text>
      <view class="time-list">
        <view class="time-slot {{selectedTimeSlot === item.id ? 'selected' : ''}}" 
              wx:for="{{timeSlots}}" 
              wx:key="id"
              bindtap="onTimeSlotSelect" 
              data-id="{{item.id}}">
          {{item.label}}
        </view>
      </view>
    </view>
  </view>

  <!-- 商品信息 -->
  <view class="products-section card">
    <view class="section-header">
      <icon type="shop" size="16" color="#1AAD19"></icon>
      <text class="section-title">山姆会员店代购</text>
      <text class="product-count">共{{orderItems.length}}件商品</text>
    </view>
    
    <view class="product-list">
      <view class="product-item" wx:for="{{orderItems}}" wx:key="id">
        <image class="product-image" src="{{item.image}}" mode="aspectFill"></image>
        <view class="product-info">
          <text class="product-title">{{item.name}}</text>
          <text class="product-spec" wx:if="{{item.selectedSpec}}">{{item.specName}}</text>
          <view class="product-price-row">
            <text class="product-price">¥{{item.price}}</text>
            <text class="product-quantity">x{{item.quantity}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 代购说明 -->
  <view class="service-section card">
    <view class="section-header">
      <icon type="info" size="16" color="#1AAD19"></icon>
      <text class="section-title">代购说明</text>
    </view>
    
    <view class="service-info">
      <view class="service-item">
        <text class="service-label">服务费：</text>
        <text class="service-value">商品总价的5%</text>
      </view>
      <view class="service-item">
        <text class="service-label">配送费：</text>
        <text class="service-value">¥{{deliveryFee}}</text>
      </view>
      <view class="service-item">
        <text class="service-label">代购时间：</text>
        <text class="service-value">当日或次日采购</text>
      </view>
    </view>
    
    <view class="service-note">
      <text class="note-text">* 商品价格以山姆会员店实际价格为准，如有差价会及时联系您</text>
    </view>
  </view>

  <!-- 备注信息 -->
  <view class="note-section card">
    <view class="section-header">
      <icon type="edit" size="16" color="#1AAD19"></icon>
      <text class="section-title">订单备注</text>
    </view>
    
    <textarea class="note-input" 
              placeholder="请输入订单备注（选填）" 
              value="{{orderNote}}"
              bindinput="onNoteInput"
              maxlength="200"></textarea>
  </view>

  <!-- 优惠券 -->
  <view class="coupon-section card" bindtap="onCouponSelect">
    <view class="coupon-header">
      <icon type="ticket" size="16" color="#1AAD19"></icon>
      <text class="coupon-title">优惠券</text>
      <icon type="arrow" size="16" color="#ccc"></icon>
    </view>
    
    <view class="coupon-content">
      <text class="coupon-text" wx:if="{{selectedCoupon}}">
        已选择：{{selectedCoupon.name}} -¥{{selectedCoupon.discount}}
      </text>
      <text class="coupon-text" wx:else>
        {{availableCoupons.length > 0 ? '选择优惠券' : '暂无可用优惠券'}}
      </text>
    </view>
  </view>
</view>

<!-- 底部结算栏 -->
<view class="bottom-bar">
  <view class="price-summary">
    <view class="price-row">
      <text class="price-label">商品总价：</text>
      <text class="price-value">¥{{subtotal}}</text>
    </view>
    <view class="price-row">
      <text class="price-label">服务费：</text>
      <text class="price-value">¥{{serviceFee}}</text>
    </view>
    <view class="price-row">
      <text class="price-label">配送费：</text>
      <text class="price-value">¥{{deliveryFee}}</text>
    </view>
    <view class="price-row" wx:if="{{couponDiscount > 0}}">
      <text class="price-label">优惠券：</text>
      <text class="price-value discount">-¥{{couponDiscount}}</text>
    </view>
    <view class="price-row total-row">
      <text class="price-label">实付金额：</text>
      <text class="total-price">¥{{totalPrice}}</text>
    </view>
  </view>
  
  <button class="submit-btn" 
          bindtap="onSubmitOrder" 
          disabled="{{!selectedAddress}}">
    提交订单
  </button>
</view>
