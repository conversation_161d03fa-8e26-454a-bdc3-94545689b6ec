// pages/profile/profile.js
const app = getApp()

Page({
  data: {
    userInfo: {},
    orderStats: {
      total: 0,
      pending: 0,
      processing: 0,
      shipping: 0,
      completed: 0,
      refund: 0
    },
    couponStats: {
      available: 0,
      used: 0,
      expired: 0
    },
    userStats: {
      points: 0,
      level: '普通会员'
    },
    recommendProducts: []
  },

  onLoad() {
    this.loadUserInfo()
    this.loadUserStats()
    this.loadRecommendProducts()
  },

  onShow() {
    // 每次显示页面时刷新数据
    this.loadUserStats()
  },

  loadUserInfo() {
    // 获取用户信息
    if (app.globalData.userInfo) {
      this.setData({
        userInfo: app.globalData.userInfo
      })
    } else {
      // 尝试从本地获取用户信息
      wx.getSetting({
        success: res => {
          if (res.authSetting['scope.userInfo']) {
            wx.getUserInfo({
              success: res => {
                this.setData({
                  userInfo: res.userInfo
                })
                app.globalData.userInfo = res.userInfo
              }
            })
          }
        }
      })
    }
  },

  loadUserStats() {
    // 模拟用户统计数据
    const mockStats = {
      orderStats: {
        total: 15,
        pending: 1,
        processing: 2,
        shipping: 1,
        completed: 11,
        refund: 0
      },
      couponStats: {
        available: 3,
        used: 5,
        expired: 2
      },
      userStats: {
        points: 1280,
        level: '普通会员'
      }
    }

    this.setData({
      orderStats: mockStats.orderStats,
      couponStats: mockStats.couponStats,
      userStats: mockStats.userStats
    })
  },

  loadRecommendProducts() {
    // 模拟推荐商品
    const recommendProducts = [
      {
        id: 201,
        name: '澳洲牛肉',
        price: 89.9,
        image: 'https://via.placeholder.com/200x200/FF6B6B/ffffff?text=牛肉'
      },
      {
        id: 202,
        name: '新西兰奇异果',
        price: 39.9,
        image: 'https://via.placeholder.com/200x200/4ECDC4/ffffff?text=奇异果'
      },
      {
        id: 203,
        name: '进口牛奶',
        price: 45.9,
        image: 'https://via.placeholder.com/200x200/45B7D1/ffffff?text=牛奶'
      },
      {
        id: 204,
        name: '有机鸡蛋',
        price: 28.9,
        image: 'https://via.placeholder.com/200x200/FFEAA7/ffffff?text=鸡蛋'
      }
    ]

    this.setData({
      recommendProducts: recommendProducts
    })
  },

  onLogin() {
    wx.getUserProfile({
      desc: '用于完善用户资料',
      success: (res) => {
        this.setData({
          userInfo: res.userInfo
        })
        app.globalData.userInfo = res.userInfo
        
        wx.showToast({
          title: '登录成功',
          icon: 'success'
        })
      },
      fail: (err) => {
        console.error('获取用户信息失败', err)
        wx.showToast({
          title: '登录失败',
          icon: 'none'
        })
      }
    })
  },

  onOrdersTap(e) {
    const status = e.currentTarget.dataset.status || 'all'
    wx.navigateTo({
      url: `/pages/order-list/order-list?status=${status}`
    })
  },

  onCouponsTap() {
    wx.navigateTo({
      url: '/pages/coupons/coupons'
    })
  },

  onPointsTap() {
    wx.navigateTo({
      url: '/pages/points/points'
    })
  },

  onMenuTap(e) {
    const page = e.currentTarget.dataset.page
    
    const pageMap = {
      address: '/pages/address/address',
      coupons: '/pages/coupons/coupons',
      favorites: '/pages/favorites/favorites',
      history: '/pages/history/history'
    }
    
    const url = pageMap[page]
    if (url) {
      wx.navigateTo({ url })
    } else {
      wx.showToast({
        title: '功能开发中',
        icon: 'none'
      })
    }
  },

  onServiceTap(e) {
    const type = e.currentTarget.dataset.type
    
    switch (type) {
      case 'contact':
        this.contactService()
        break
      case 'feedback':
        this.showFeedback()
        break
      case 'about':
        this.showAbout()
        break
      case 'settings':
        this.showSettings()
        break
      default:
        wx.showToast({
          title: '功能开发中',
          icon: 'none'
        })
    }
  },

  contactService() {
    wx.showModal({
      title: '联系客服',
      content: '客服微信：sams_service\n客服电话：400-123-4567\n服务时间：9:00-21:00',
      showCancel: false,
      confirmText: '知道了'
    })
  },

  showFeedback() {
    wx.showModal({
      title: '意见反馈',
      content: '请通过客服微信或电话联系我们，我们会认真处理您的每一条建议。',
      showCancel: false,
      confirmText: '知道了'
    })
  },

  showAbout() {
    wx.showModal({
      title: '关于我们',
      content: '山姆代购小程序\n版本：1.0.0\n专业的山姆会员店代购服务\n为您提供优质商品和贴心服务',
      showCancel: false,
      confirmText: '知道了'
    })
  },

  showSettings() {
    wx.showActionSheet({
      itemList: ['清除缓存', '检查更新', '隐私设置'],
      success: (res) => {
        const actions = ['清除缓存', '检查更新', '隐私设置']
        wx.showToast({
          title: `${actions[res.tapIndex]}功能开发中`,
          icon: 'none'
        })
      }
    })
  },

  onProductTap(e) {
    const product = e.currentTarget.dataset.product
    wx.navigateTo({
      url: `/pages/product-detail/product-detail?productId=${product.id}`
    })
  },

  onPullDownRefresh() {
    this.loadUserStats()
    this.loadRecommendProducts()
    wx.stopPullDownRefresh()
  },

  onShareAppMessage() {
    return {
      title: '山姆代购 - 优质商品直达',
      path: '/pages/index/index'
    }
  }
})
