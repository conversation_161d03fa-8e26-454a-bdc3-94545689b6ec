/* pages/profile/profile.wxss */

/* 用户信息 */
.user-info {
  padding: 40rpx 30rpx;
  margin-bottom: 20rpx;
}

.user-avatar-section {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  margin-right: 30rpx;
  border: 4rpx solid #f0f0f0;
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
  display: block;
}

.user-level {
  font-size: 24rpx;
  color: #666;
  background: #f0f9ff;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  display: inline-block;
}

.login-btn {
  background: #1AAD19;
  color: white;
  padding: 15rpx 30rpx;
  border-radius: 30rpx;
  font-size: 28rpx;
}

.user-stats {
  display: flex;
  justify-content: space-around;
  padding-top: 30rpx;
  border-top: 1rpx solid #f0f0f0;
}

.stat-item {
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-number {
  font-size: 36rpx;
  font-weight: 600;
  color: #1AAD19;
  margin-bottom: 10rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

/* 订单状态 */
.order-status {
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.section-more {
  font-size: 28rpx;
  color: #1AAD19;
}

.order-status-list {
  display: flex;
  justify-content: space-around;
}

.status-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.status-icon {
  position: relative;
  margin-bottom: 15rpx;
}

.status-badge {
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  background: #e74c3c;
  color: white;
  border-radius: 50%;
  width: 36rpx;
  height: 36rpx;
  font-size: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 36rpx;
}

.status-text {
  font-size: 24rpx;
  color: #666;
}

/* 功能菜单 */
.menu-section,
.service-menu {
  margin-bottom: 20rpx;
}

.menu-list {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-icon {
  width: 60rpx;
  height: 60rpx;
  background: #f8f8f8;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 30rpx;
}

.menu-text {
  flex: 1;
  font-size: 32rpx;
  color: #333;
}

.menu-badge {
  background: #e74c3c;
  color: white;
  border-radius: 50%;
  width: 36rpx;
  height: 36rpx;
  font-size: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  min-width: 36rpx;
}

/* 推荐商品 */
.recommend-section {
  margin-bottom: 40rpx;
}

.recommend-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  padding: 0 30rpx;
}

.recommend-item {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.recommend-image {
  width: 100%;
  height: 200rpx;
  object-fit: cover;
}

.recommend-title {
  font-size: 28rpx;
  color: #333;
  padding: 20rpx 20rpx 10rpx;
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.recommend-price {
  font-size: 32rpx;
  font-weight: 600;
  color: #e74c3c;
  padding: 0 20rpx 20rpx;
  display: block;
}

/* 响应式调整 */
@media (max-width: 400px) {
  .user-avatar {
    width: 100rpx;
    height: 100rpx;
  }
  
  .user-name {
    font-size: 32rpx;
  }
  
  .stat-number {
    font-size: 32rpx;
  }
  
  .recommend-grid {
    grid-template-columns: 1fr;
  }
}
