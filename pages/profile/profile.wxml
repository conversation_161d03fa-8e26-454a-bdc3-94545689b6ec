<!--pages/profile/profile.wxml-->
<view class="container">
  <!-- 用户信息 -->
  <view class="user-info card">
    <view class="user-avatar-section">
      <image class="user-avatar" src="{{userInfo.avatarUrl || '/images/default-avatar.png'}}" mode="aspectFill"></image>
      <view class="user-details">
        <text class="user-name">{{userInfo.nickName || '点击登录'}}</text>
        <text class="user-level">普通会员</text>
      </view>
      <view class="login-btn" wx:if="{{!userInfo.nickName}}" bindtap="onLogin">
        <text>登录</text>
      </view>
    </view>
    
    <view class="user-stats">
      <view class="stat-item" bindtap="onOrdersTap" data-status="all">
        <text class="stat-number">{{orderStats.total}}</text>
        <text class="stat-label">全部订单</text>
      </view>
      <view class="stat-item" bindtap="onCouponsTap">
        <text class="stat-number">{{couponStats.available}}</text>
        <text class="stat-label">优惠券</text>
      </view>
      <view class="stat-item" bindtap="onPointsTap">
        <text class="stat-number">{{userStats.points}}</text>
        <text class="stat-label">积分</text>
      </view>
    </view>
  </view>

  <!-- 订单状态 -->
  <view class="order-status card">
    <view class="section-header">
      <text class="section-title">我的订单</text>
      <text class="section-more" bindtap="onOrdersTap" data-status="all">查看全部 ></text>
    </view>
    
    <view class="order-status-list">
      <view class="status-item" bindtap="onOrdersTap" data-status="pending">
        <view class="status-icon">
          <icon type="time" size="24" color="#ff9500"></icon>
          <view class="status-badge" wx:if="{{orderStats.pending > 0}}">{{orderStats.pending}}</view>
        </view>
        <text class="status-text">待付款</text>
      </view>
      
      <view class="status-item" bindtap="onOrdersTap" data-status="processing">
        <view class="status-icon">
          <icon type="shop" size="24" color="#1AAD19"></icon>
          <view class="status-badge" wx:if="{{orderStats.processing > 0}}">{{orderStats.processing}}</view>
        </view>
        <text class="status-text">代购中</text>
      </view>
      
      <view class="status-item" bindtap="onOrdersTap" data-status="shipping">
        <view class="status-icon">
          <icon type="location" size="24" color="#007aff"></icon>
          <view class="status-badge" wx:if="{{orderStats.shipping > 0}}">{{orderStats.shipping}}</view>
        </view>
        <text class="status-text">配送中</text>
      </view>
      
      <view class="status-item" bindtap="onOrdersTap" data-status="completed">
        <view class="status-icon">
          <icon type="success" size="24" color="#34c759"></icon>
        </view>
        <text class="status-text">已完成</text>
      </view>
      
      <view class="status-item" bindtap="onOrdersTap" data-status="refund">
        <view class="status-icon">
          <icon type="info" size="24" color="#ff3b30"></icon>
          <view class="status-badge" wx:if="{{orderStats.refund > 0}}">{{orderStats.refund}}</view>
        </view>
        <text class="status-text">退款/售后</text>
      </view>
    </view>
  </view>

  <!-- 功能菜单 -->
  <view class="menu-section card">
    <view class="menu-list">
      <view class="menu-item" bindtap="onMenuTap" data-page="address">
        <view class="menu-icon">
          <icon type="location" size="20" color="#1AAD19"></icon>
        </view>
        <text class="menu-text">收货地址</text>
        <icon type="arrow" size="16" color="#ccc"></icon>
      </view>
      
      <view class="menu-item" bindtap="onMenuTap" data-page="coupons">
        <view class="menu-icon">
          <icon type="ticket" size="20" color="#ff9500"></icon>
        </view>
        <text class="menu-text">我的优惠券</text>
        <view class="menu-badge" wx:if="{{couponStats.available > 0}}">{{couponStats.available}}</view>
        <icon type="arrow" size="16" color="#ccc"></icon>
      </view>
      
      <view class="menu-item" bindtap="onMenuTap" data-page="favorites">
        <view class="menu-icon">
          <icon type="heart" size="20" color="#ff3b30"></icon>
        </view>
        <text class="menu-text">我的收藏</text>
        <icon type="arrow" size="16" color="#ccc"></icon>
      </view>
      
      <view class="menu-item" bindtap="onMenuTap" data-page="history">
        <view class="menu-icon">
          <icon type="time" size="20" color="#666"></icon>
        </view>
        <text class="menu-text">浏览历史</text>
        <icon type="arrow" size="16" color="#ccc"></icon>
      </view>
    </view>
  </view>

  <!-- 服务菜单 -->
  <view class="service-menu card">
    <view class="menu-list">
      <view class="menu-item" bindtap="onServiceTap" data-type="contact">
        <view class="menu-icon">
          <icon type="chat" size="20" color="#007aff"></icon>
        </view>
        <text class="menu-text">联系客服</text>
        <icon type="arrow" size="16" color="#ccc"></icon>
      </view>
      
      <view class="menu-item" bindtap="onServiceTap" data-type="feedback">
        <view class="menu-icon">
          <icon type="edit" size="20" color="#34c759"></icon>
        </view>
        <text class="menu-text">意见反馈</text>
        <icon type="arrow" size="16" color="#ccc"></icon>
      </view>
      
      <view class="menu-item" bindtap="onServiceTap" data-type="about">
        <view class="menu-icon">
          <icon type="info" size="20" color="#666"></icon>
        </view>
        <text class="menu-text">关于我们</text>
        <icon type="arrow" size="16" color="#ccc"></icon>
      </view>
      
      <view class="menu-item" bindtap="onServiceTap" data-type="settings">
        <view class="menu-icon">
          <icon type="settings" size="20" color="#666"></icon>
        </view>
        <text class="menu-text">设置</text>
        <icon type="arrow" size="16" color="#ccc"></icon>
      </view>
    </view>
  </view>

  <!-- 推荐商品 -->
  <view class="recommend-section" wx:if="{{recommendProducts.length > 0}}">
    <view class="section-header">
      <text class="section-title">为您推荐</text>
    </view>
    
    <view class="recommend-grid">
      <view class="recommend-item" wx:for="{{recommendProducts}}" wx:key="id" bindtap="onProductTap" data-product="{{item}}">
        <image class="recommend-image" src="{{item.image}}" mode="aspectFill"></image>
        <text class="recommend-title">{{item.name}}</text>
        <text class="recommend-price">¥{{item.price}}</text>
      </view>
    </view>
  </view>
</view>
