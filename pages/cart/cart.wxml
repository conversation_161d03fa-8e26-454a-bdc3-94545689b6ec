<!--pages/cart/cart.wxml-->
<view class="container">
  <!-- 购物车为空 -->
  <view wx:if="{{cartItems.length === 0}}" class="empty-cart">
    <view class="empty-icon">🛒</view>
    <text class="empty-text">购物车还是空的</text>
    <text class="empty-desc">快去选购心仪的商品吧</text>
    <button class="btn-primary" bindtap="onGoShopping">去购物</button>
  </view>

  <!-- 购物车有商品 -->
  <view wx:else>
    <!-- 店铺信息 -->
    <view class="shop-info card">
      <view class="shop-header">
        <checkbox class="shop-checkbox" checked="{{allSelected}}" bindtap="onSelectAll"></checkbox>
        <text class="shop-name">山姆会员店代购</text>
      </view>
      <text class="shop-desc">专业代购服务，品质保证</text>
    </view>

    <!-- 商品列表 -->
    <view class="cart-items">
      <view class="cart-item card" wx:for="{{cartItems}}" wx:key="id">
        <view class="item-header">
          <checkbox class="item-checkbox" 
                   checked="{{item.selected}}" 
                   bindtap="onSelectItem" 
                   data-id="{{item.id}}"></checkbox>
          <image class="item-image" src="{{item.image}}" mode="aspectFill"></image>
          <view class="item-info">
            <text class="item-title">{{item.name}}</text>
            <text class="item-spec" wx:if="{{item.selectedSpec}}">{{item.specName}}</text>
            <view class="item-price-row">
              <text class="item-price">¥{{item.price}}</text>
              <text class="item-original-price" wx:if="{{item.originalPrice}}">¥{{item.originalPrice}}</text>
            </view>
          </view>
        </view>
        
        <view class="item-footer">
          <view class="quantity-controls">
            <button class="quantity-btn" 
                    bindtap="onQuantityChange" 
                    data-id="{{item.id}}" 
                    data-type="minus"
                    disabled="{{item.quantity <= 1}}">-</button>
            <input class="quantity-input" 
                   type="number" 
                   value="{{item.quantity}}" 
                   bindinput="onQuantityInput"
                   data-id="{{item.id}}"></input>
            <button class="quantity-btn" 
                    bindtap="onQuantityChange" 
                    data-id="{{item.id}}" 
                    data-type="plus">+</button>
          </view>
          
          <button class="delete-btn" bindtap="onDeleteItem" data-id="{{item.id}}">
            <icon type="clear" size="16"></icon>
          </button>
        </view>
      </view>
    </view>

    <!-- 推荐商品 -->
    <view class="recommend-section" wx:if="{{recommendProducts.length > 0}}">
      <view class="section-title">为您推荐</view>
      <scroll-view class="recommend-scroll" scroll-x="true">
        <view class="recommend-list">
          <view class="recommend-item" wx:for="{{recommendProducts}}" wx:key="id" bindtap="onRecommendTap" data-product="{{item}}">
            <image class="recommend-image" src="{{item.image}}" mode="aspectFill"></image>
            <text class="recommend-title">{{item.name}}</text>
            <text class="recommend-price">¥{{item.price}}</text>
          </view>
        </view>
      </scroll-view>
    </view>
  </view>
</view>

<!-- 底部结算栏 -->
<view class="bottom-bar" wx:if="{{cartItems.length > 0}}">
  <view class="select-all">
    <checkbox checked="{{allSelected}}" bindtap="onSelectAll"></checkbox>
    <text class="select-text">全选</text>
  </view>
  
  <view class="price-info">
    <view class="price-row">
      <text class="price-label">商品总价:</text>
      <text class="subtotal">¥{{subtotal}}</text>
    </view>
    <view class="price-row">
      <text class="price-label">服务费:</text>
      <text class="service-fee">¥{{serviceFee}}</text>
    </view>
    <view class="price-row">
      <text class="price-label">配送费:</text>
      <text class="delivery-fee">¥{{deliveryFee}}</text>
    </view>
    <view class="price-row total-row">
      <text class="price-label">合计:</text>
      <text class="total-price">¥{{totalPrice}}</text>
    </view>
  </view>
  
  <button class="checkout-btn" 
          bindtap="onCheckout" 
          disabled="{{selectedCount === 0}}">
    结算({{selectedCount}})
  </button>
</view>
