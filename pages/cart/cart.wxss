/* pages/cart/cart.wxss */

.container {
  padding-bottom: 200rpx; /* 为底部结算栏留出空间 */
}

/* 空购物车 */
.empty-cart {
  text-align: center;
  padding: 200rpx 40rpx;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 36rpx;
  color: #333;
  margin-bottom: 15rpx;
  display: block;
}

.empty-desc {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 60rpx;
  display: block;
}

/* 店铺信息 */
.shop-info {
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.shop-header {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}

.shop-checkbox {
  margin-right: 20rpx;
}

.shop-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.shop-desc {
  font-size: 24rpx;
  color: #666;
  margin-left: 60rpx;
}

/* 购物车商品 */
.cart-items {
  margin-bottom: 40rpx;
}

.cart-item {
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.item-header {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.item-checkbox {
  margin-right: 20rpx;
  margin-top: 10rpx;
}

.item-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
  object-fit: cover;
}

.item-info {
  flex: 1;
}

.item-title {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 10rpx;
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.item-spec {
  font-size: 24rpx;
  color: #666;
  background: #f0f0f0;
  padding: 6rpx 12rpx;
  border-radius: 6rpx;
  margin-bottom: 15rpx;
  display: inline-block;
}

.item-price-row {
  display: flex;
  align-items: center;
}

.item-price {
  font-size: 32rpx;
  font-weight: 600;
  color: #e74c3c;
  margin-right: 15rpx;
}

.item-original-price {
  font-size: 24rpx;
  color: #999;
  text-decoration: line-through;
}

.item-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.quantity-controls {
  display: flex;
  align-items: center;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  overflow: hidden;
}

.quantity-btn {
  width: 60rpx;
  height: 60rpx;
  background: #f8f8f8;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #666;
  padding: 0;
  margin: 0;
}

.quantity-btn::after {
  border: none;
}

.quantity-btn:disabled {
  opacity: 0.5;
}

.quantity-btn:active:not(:disabled) {
  background: #e8e8e8;
}

.quantity-input {
  width: 80rpx;
  height: 60rpx;
  text-align: center;
  border: none;
  font-size: 28rpx;
  background: white;
}

.delete-btn {
  width: 60rpx;
  height: 60rpx;
  background: #f8f8f8;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  padding: 0;
  margin: 0;
}

.delete-btn::after {
  border: none;
}

.delete-btn:active {
  background: #e8e8e8;
}

/* 推荐商品 */
.recommend-section {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  padding: 0 30rpx 20rpx;
}

.recommend-scroll {
  white-space: nowrap;
}

.recommend-list {
  display: inline-flex;
  gap: 20rpx;
  padding: 0 30rpx;
}

.recommend-item {
  width: 200rpx;
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
}

.recommend-image {
  width: 100%;
  height: 150rpx;
  object-fit: cover;
}

.recommend-title {
  font-size: 24rpx;
  color: #333;
  padding: 15rpx 15rpx 10rpx;
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.recommend-price {
  font-size: 28rpx;
  font-weight: 600;
  color: #e74c3c;
  padding: 0 15rpx 15rpx;
  display: block;
}

/* 底部结算栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #eee;
  display: flex;
  align-items: center;
  gap: 20rpx;
  z-index: 100;
}

.select-all {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.select-text {
  font-size: 28rpx;
  color: #333;
}

.price-info {
  flex: 1;
  padding: 0 20rpx;
}

.price-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5rpx;
}

.price-row.total-row {
  margin-bottom: 0;
  padding-top: 10rpx;
  border-top: 1rpx solid #eee;
}

.price-label {
  font-size: 24rpx;
  color: #666;
}

.subtotal,
.service-fee,
.delivery-fee {
  font-size: 24rpx;
  color: #333;
}

.total-price {
  font-size: 32rpx;
  font-weight: 600;
  color: #e74c3c;
}

.checkout-btn {
  background: #1AAD19;
  color: white;
  border: none;
  border-radius: 50rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  min-width: 160rpx;
}

.checkout-btn:disabled {
  background: #ccc;
}
