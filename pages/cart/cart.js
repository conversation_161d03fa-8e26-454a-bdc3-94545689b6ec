// pages/cart/cart.js
const app = getApp()

Page({
  data: {
    cartItems: [],
    allSelected: false,
    selectedCount: 0,
    subtotal: 0,
    serviceFee: 0,
    deliveryFee: 10,
    totalPrice: 0,
    recommendProducts: []
  },

  onLoad() {
    this.loadRecommendProducts()
  },

  onShow() {
    this.loadCartData()
  },

  loadCartData() {
    const cartItems = app.globalData.cart.map(item => ({
      ...item,
      selected: true
    }))
    
    this.setData({
      cartItems: cartItems
    })
    
    this.calculatePrice()
    this.checkAllSelected()
  },

  loadRecommendProducts() {
    // 模拟推荐商品数据
    const recommendProducts = [
      {
        id: 101,
        name: '新西兰奇异果',
        price: 39.9,
        image: 'https://via.placeholder.com/200x200/4ECDC4/ffffff?text=奇异果'
      },
      {
        id: 102,
        name: '进口牛奶',
        price: 45.9,
        image: 'https://via.placeholder.com/200x200/45B7D1/ffffff?text=牛奶'
      },
      {
        id: 103,
        name: '有机鸡蛋',
        price: 28.9,
        image: 'https://via.placeholder.com/200x200/FFEAA7/ffffff?text=鸡蛋'
      }
    ]
    
    this.setData({
      recommendProducts: recommendProducts
    })
  },

  onSelectAll() {
    const { allSelected, cartItems } = this.data
    const newAllSelected = !allSelected
    
    const updatedItems = cartItems.map(item => ({
      ...item,
      selected: newAllSelected
    }))
    
    this.setData({
      cartItems: updatedItems,
      allSelected: newAllSelected
    })
    
    this.calculatePrice()
  },

  onSelectItem(e) {
    const itemId = e.currentTarget.dataset.id
    const { cartItems } = this.data
    
    const updatedItems = cartItems.map(item => {
      if (item.id === itemId) {
        return { ...item, selected: !item.selected }
      }
      return item
    })
    
    this.setData({
      cartItems: updatedItems
    })
    
    this.calculatePrice()
    this.checkAllSelected()
  },

  checkAllSelected() {
    const { cartItems } = this.data
    const allSelected = cartItems.length > 0 && cartItems.every(item => item.selected)
    
    this.setData({
      allSelected: allSelected
    })
  },

  onQuantityChange(e) {
    const { id, type } = e.currentTarget.dataset
    const { cartItems } = this.data
    
    const updatedItems = cartItems.map(item => {
      if (item.id === id) {
        let newQuantity = item.quantity
        if (type === 'plus') {
          newQuantity += 1
        } else if (type === 'minus' && newQuantity > 1) {
          newQuantity -= 1
        }
        
        // 更新全局购物车
        app.updateCartQuantity(id, newQuantity)
        
        return { ...item, quantity: newQuantity }
      }
      return item
    })
    
    this.setData({
      cartItems: updatedItems
    })
    
    this.calculatePrice()
  },

  onQuantityInput(e) {
    const itemId = e.currentTarget.dataset.id
    const value = parseInt(e.detail.value) || 1
    const { cartItems } = this.data
    
    const updatedItems = cartItems.map(item => {
      if (item.id === itemId) {
        const newQuantity = Math.max(1, value)
        
        // 更新全局购物车
        app.updateCartQuantity(itemId, newQuantity)
        
        return { ...item, quantity: newQuantity }
      }
      return item
    })
    
    this.setData({
      cartItems: updatedItems
    })
    
    this.calculatePrice()
  },

  onDeleteItem(e) {
    const itemId = e.currentTarget.dataset.id
    
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这个商品吗？',
      success: (res) => {
        if (res.confirm) {
          // 从全局购物车删除
          app.removeFromCart(itemId)
          
          // 更新页面数据
          const updatedItems = this.data.cartItems.filter(item => item.id !== itemId)
          this.setData({
            cartItems: updatedItems
          })
          
          this.calculatePrice()
          this.checkAllSelected()
          
          wx.showToast({
            title: '已删除',
            icon: 'success'
          })
        }
      }
    })
  },

  calculatePrice() {
    const { cartItems } = this.data
    const selectedItems = cartItems.filter(item => item.selected)
    
    const subtotal = selectedItems.reduce((total, item) => {
      return total + (item.price * item.quantity)
    }, 0)
    
    const serviceFee = (subtotal * app.globalData.serviceRate).toFixed(2)
    const deliveryFee = selectedItems.length > 0 ? app.globalData.deliveryFee : 0
    const totalPrice = (parseFloat(subtotal) + parseFloat(serviceFee) + deliveryFee).toFixed(2)
    
    this.setData({
      selectedCount: selectedItems.length,
      subtotal: subtotal.toFixed(2),
      serviceFee: serviceFee,
      deliveryFee: deliveryFee,
      totalPrice: totalPrice
    })
  },

  onCheckout() {
    const { cartItems } = this.data
    const selectedItems = cartItems.filter(item => item.selected)
    
    if (selectedItems.length === 0) {
      wx.showToast({
        title: '请选择商品',
        icon: 'none'
      })
      return
    }
    
    wx.navigateTo({
      url: `/pages/order/order?type=cart&items=${encodeURIComponent(JSON.stringify(selectedItems))}`
    })
  },

  onGoShopping() {
    wx.switchTab({
      url: '/pages/index/index'
    })
  },

  onRecommendTap(e) {
    const product = e.currentTarget.dataset.product
    wx.navigateTo({
      url: `/pages/product-detail/product-detail?productId=${product.id}`
    })
  },

  onPullDownRefresh() {
    this.loadCartData()
    this.loadRecommendProducts()
    wx.stopPullDownRefresh()
  }
})
