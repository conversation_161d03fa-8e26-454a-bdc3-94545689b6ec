<!--pages/order-detail/order-detail.wxml-->
<view class="container">
  <!-- 订单状态 -->
  <view class="order-status card">
    <view class="status-icon">
      <icon type="{{statusIcon}}" size="40" color="{{statusColor}}"></icon>
    </view>
    <view class="status-info">
      <text class="status-text">{{order.statusText}}</text>
      <text class="status-desc">{{order.statusDesc}}</text>
    </view>
  </view>

  <!-- 物流信息 -->
  <view class="logistics-info card" wx:if="{{order.logistics}}">
    <view class="section-header">
      <icon type="location" size="16" color="#1AAD19"></icon>
      <text class="section-title">物流信息</text>
    </view>
    
    <view class="logistics-content">
      <view class="logistics-item" wx:for="{{order.logistics.tracks}}" wx:key="time">
        <view class="track-time">{{item.time}}</view>
        <view class="track-desc">{{item.desc}}</view>
      </view>
    </view>
    
    <view class="logistics-footer">
      <text class="courier-info">{{order.logistics.courier}} {{order.logistics.phone}}</text>
      <button class="contact-btn" bindtap="onContactCourier">联系配送员</button>
    </view>
  </view>

  <!-- 收货地址 -->
  <view class="address-info card">
    <view class="section-header">
      <icon type="location" size="16" color="#1AAD19"></icon>
      <text class="section-title">收货地址</text>
    </view>
    
    <view class="address-content">
      <view class="receiver-info">
        <text class="receiver-name">{{order.address.name}}</text>
        <text class="receiver-phone">{{order.address.phone}}</text>
      </view>
      <text class="address-detail">{{order.address.fullAddress}}</text>
    </view>
  </view>

  <!-- 商品信息 -->
  <view class="products-info card">
    <view class="section-header">
      <icon type="shop" size="16" color="#1AAD19"></icon>
      <text class="section-title">山姆会员店代购</text>
      <text class="order-number">订单号：{{order.orderNo}}</text>
    </view>
    
    <view class="product-list">
      <view class="product-item" wx:for="{{order.products}}" wx:key="id">
        <image class="product-image" src="{{item.image}}" mode="aspectFill"></image>
        <view class="product-info">
          <text class="product-title">{{item.name}}</text>
          <text class="product-spec" wx:if="{{item.spec}}">{{item.spec}}</text>
          <view class="product-price-row">
            <text class="product-price">¥{{item.price}}</text>
            <text class="product-quantity">x{{item.quantity}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 订单信息 -->
  <view class="order-info card">
    <view class="section-header">
      <icon type="info" size="16" color="#1AAD19"></icon>
      <text class="section-title">订单信息</text>
    </view>
    
    <view class="order-details">
      <view class="detail-item">
        <text class="detail-label">订单编号：</text>
        <text class="detail-value">{{order.orderNo}}</text>
      </view>
      <view class="detail-item">
        <text class="detail-label">下单时间：</text>
        <text class="detail-value">{{order.createTime}}</text>
      </view>
      <view class="detail-item" wx:if="{{order.payTime}}">
        <text class="detail-label">付款时间：</text>
        <text class="detail-value">{{order.payTime}}</text>
      </view>
      <view class="detail-item" wx:if="{{order.deliveryTime}}">
        <text class="detail-label">配送时间：</text>
        <text class="detail-value">{{order.deliveryTime}}</text>
      </view>
      <view class="detail-item" wx:if="{{order.completeTime}}">
        <text class="detail-label">完成时间：</text>
        <text class="detail-value">{{order.completeTime}}</text>
      </view>
      <view class="detail-item" wx:if="{{order.note}}">
        <text class="detail-label">订单备注：</text>
        <text class="detail-value">{{order.note}}</text>
      </view>
    </view>
  </view>

  <!-- 费用明细 -->
  <view class="price-detail card">
    <view class="section-header">
      <icon type="money" size="16" color="#1AAD19"></icon>
      <text class="section-title">费用明细</text>
    </view>
    
    <view class="price-list">
      <view class="price-item">
        <text class="price-label">商品总价</text>
        <text class="price-value">¥{{order.subtotal}}</text>
      </view>
      <view class="price-item">
        <text class="price-label">服务费</text>
        <text class="price-value">¥{{order.serviceFee}}</text>
      </view>
      <view class="price-item">
        <text class="price-label">配送费</text>
        <text class="price-value">¥{{order.deliveryFee}}</text>
      </view>
      <view class="price-item" wx:if="{{order.couponDiscount > 0}}">
        <text class="price-label">优惠券</text>
        <text class="price-value discount">-¥{{order.couponDiscount}}</text>
      </view>
      <view class="price-item total">
        <text class="price-label">实付金额</text>
        <text class="price-value total-price">¥{{order.totalPrice}}</text>
      </view>
    </view>
  </view>
</view>

<!-- 底部操作栏 -->
<view class="bottom-actions" wx:if="{{actionButtons.length > 0}}">
  <button class="action-btn {{item.type}}" 
          wx:for="{{actionButtons}}" 
          wx:key="type"
          bindtap="onActionTap" 
          data-action="{{item.type}}">
    {{item.text}}
  </button>
</view>
