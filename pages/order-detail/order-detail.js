// pages/order-detail/order-detail.js
Page({
  data: {
    orderId: '',
    order: {},
    statusIcon: 'time',
    statusColor: '#ff9500',
    actionButtons: []
  },

  onLoad(options) {
    const orderId = options.orderId
    this.setData({ orderId })
    this.loadOrderDetail(orderId)
  },

  loadOrderDetail(orderId) {
    wx.showLoading({
      title: '加载中...'
    })

    // 模拟订单详情数据
    const mockOrder = {
      id: orderId,
      orderNo: orderId,
      status: 'shipping', // pending, paid, processing, shipping, completed, cancelled, refund
      statusText: '配送中',
      statusDesc: '您的订单正在配送途中，请耐心等待',
      createTime: '2024-01-15 14:30:00',
      payTime: '2024-01-15 14:32:00',
      deliveryTime: '2024-01-15 16:00-18:00',
      completeTime: null,
      note: '请放在门口，谢谢',
      
      address: {
        name: '张三',
        phone: '138****8888',
        fullAddress: '北京市朝阳区三里屯街道工体北路8号院1号楼101室'
      },
      
      products: [
        {
          id: 1,
          name: '澳洲进口牛肉',
          spec: '500g装',
          price: 89.9,
          quantity: 2,
          image: 'https://via.placeholder.com/200x200/FF6B6B/ffffff?text=牛肉'
        },
        {
          id: 2,
          name: '新西兰奇异果',
          spec: '12个装',
          price: 39.9,
          quantity: 1,
          image: 'https://via.placeholder.com/200x200/4ECDC4/ffffff?text=奇异果'
        }
      ],
      
      subtotal: 219.7,
      serviceFee: 10.99,
      deliveryFee: 10,
      couponDiscount: 20,
      totalPrice: 220.69,
      
      logistics: {
        courier: '李师傅',
        phone: '139****7777',
        tracks: [
          {
            time: '2024-01-15 16:30',
            desc: '配送员已取货，正在配送途中'
          },
          {
            time: '2024-01-15 15:45',
            desc: '商品已从山姆会员店发出'
          },
          {
            time: '2024-01-15 15:00',
            desc: '代购员已完成采购，正在打包'
          },
          {
            time: '2024-01-15 14:32',
            desc: '订单支付成功，开始代购'
          }
        ]
      }
    }

    setTimeout(() => {
      this.setData({
        order: mockOrder
      })
      this.updateStatusDisplay(mockOrder.status)
      this.updateActionButtons(mockOrder.status)
      wx.hideLoading()
    }, 1000)
  },

  updateStatusDisplay(status) {
    const statusMap = {
      pending: { icon: 'time', color: '#ff9500' },
      paid: { icon: 'shop', color: '#1AAD19' },
      processing: { icon: 'shop', color: '#1AAD19' },
      shipping: { icon: 'location', color: '#007aff' },
      completed: { icon: 'success', color: '#34c759' },
      cancelled: { icon: 'clear', color: '#999' },
      refund: { icon: 'info', color: '#ff3b30' }
    }

    const statusInfo = statusMap[status] || statusMap.pending

    this.setData({
      statusIcon: statusInfo.icon,
      statusColor: statusInfo.color
    })
  },

  updateActionButtons(status) {
    let buttons = []

    switch (status) {
      case 'pending':
        buttons = [
          { type: 'cancel', text: '取消订单' },
          { type: 'pay', text: '立即付款' }
        ]
        break
      case 'paid':
      case 'processing':
        buttons = [
          { type: 'contact', text: '联系客服' }
        ]
        break
      case 'shipping':
        buttons = [
          { type: 'contact', text: '联系客服' },
          { type: 'confirm', text: '确认收货' }
        ]
        break
      case 'completed':
        buttons = [
          { type: 'review', text: '评价订单' },
          { type: 'rebuy', text: '再次购买' }
        ]
        break
      case 'cancelled':
        buttons = [
          { type: 'rebuy', text: '再次购买' }
        ]
        break
      case 'refund':
        buttons = [
          { type: 'contact', text: '联系客服' }
        ]
        break
    }

    this.setData({
      actionButtons: buttons
    })
  },

  onContactCourier() {
    const { logistics } = this.data.order
    
    wx.showModal({
      title: '联系配送员',
      content: `配送员：${logistics.courier}\n电话：${logistics.phone}`,
      confirmText: '拨打电话',
      success: (res) => {
        if (res.confirm) {
          wx.makePhoneCall({
            phoneNumber: logistics.phone.replace(/\*/g, '1') // 模拟真实号码
          })
        }
      }
    })
  },

  onActionTap(e) {
    const action = e.currentTarget.dataset.action
    
    switch (action) {
      case 'pay':
        this.handlePay()
        break
      case 'cancel':
        this.handleCancel()
        break
      case 'confirm':
        this.handleConfirm()
        break
      case 'contact':
        this.handleContact()
        break
      case 'review':
        this.handleReview()
        break
      case 'rebuy':
        this.handleRebuy()
        break
      default:
        wx.showToast({
          title: '功能开发中',
          icon: 'none'
        })
    }
  },

  handlePay() {
    wx.showModal({
      title: '确认付款',
      content: `订单金额：¥${this.data.order.totalPrice}`,
      confirmText: '立即付款',
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '支付中...'
          })
          
          // 模拟支付
          setTimeout(() => {
            wx.hideLoading()
            wx.showToast({
              title: '支付成功',
              icon: 'success'
            })
            
            // 更新订单状态
            this.setData({
              'order.status': 'paid',
              'order.statusText': '已付款',
              'order.statusDesc': '订单已付款，代购员正在采购中',
              'order.payTime': new Date().toLocaleString()
            })
            
            this.updateStatusDisplay('paid')
            this.updateActionButtons('paid')
          }, 2000)
        }
      }
    })
  },

  handleCancel() {
    wx.showModal({
      title: '取消订单',
      content: '确定要取消这个订单吗？',
      confirmText: '确认取消',
      success: (res) => {
        if (res.confirm) {
          wx.showToast({
            title: '订单已取消',
            icon: 'success'
          })
          
          this.setData({
            'order.status': 'cancelled',
            'order.statusText': '已取消',
            'order.statusDesc': '订单已取消'
          })
          
          this.updateStatusDisplay('cancelled')
          this.updateActionButtons('cancelled')
        }
      }
    })
  },

  handleConfirm() {
    wx.showModal({
      title: '确认收货',
      content: '确认已收到商品吗？',
      confirmText: '确认收货',
      success: (res) => {
        if (res.confirm) {
          wx.showToast({
            title: '确认收货成功',
            icon: 'success'
          })
          
          this.setData({
            'order.status': 'completed',
            'order.statusText': '已完成',
            'order.statusDesc': '订单已完成，感谢您的购买',
            'order.completeTime': new Date().toLocaleString()
          })
          
          this.updateStatusDisplay('completed')
          this.updateActionButtons('completed')
        }
      }
    })
  },

  handleContact() {
    wx.showModal({
      title: '联系客服',
      content: '客服微信：sams_service\n客服电话：************\n服务时间：9:00-21:00',
      showCancel: false,
      confirmText: '知道了'
    })
  },

  handleReview() {
    wx.showToast({
      title: '评价功能开发中',
      icon: 'none'
    })
  },

  handleRebuy() {
    const { products } = this.data.order
    
    wx.showModal({
      title: '再次购买',
      content: '将订单中的商品加入购物车？',
      confirmText: '加入购物车',
      success: (res) => {
        if (res.confirm) {
          // 这里可以调用app的addToCart方法
          wx.showToast({
            title: '已加入购物车',
            icon: 'success'
          })
        }
      }
    })
  },

  onShareAppMessage() {
    return {
      title: '山姆代购订单详情',
      path: '/pages/index/index'
    }
  }
})
