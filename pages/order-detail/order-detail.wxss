/* pages/order-detail/order-detail.wxss */

.container {
  padding-bottom: 120rpx; /* 为底部操作栏留出空间 */
}

/* 订单状态 */
.order-status {
  padding: 40rpx 30rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #1AAD19, #0d8c13);
  color: white;
}

.status-icon {
  margin-right: 30rpx;
}

.status-info {
  flex: 1;
}

.status-text {
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 10rpx;
  display: block;
}

.status-desc {
  font-size: 28rpx;
  opacity: 0.9;
}

/* 物流信息 */
.logistics-info {
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-left: 15rpx;
  flex: 1;
}

.order-number {
  font-size: 24rpx;
  color: #666;
}

.logistics-content {
  margin: 20rpx 0;
}

.logistics-item {
  display: flex;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.logistics-item:last-child {
  border-bottom: none;
}

.track-time {
  width: 200rpx;
  font-size: 24rpx;
  color: #666;
  flex-shrink: 0;
}

.track-desc {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  line-height: 1.4;
}

.logistics-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.courier-info {
  font-size: 28rpx;
  color: #666;
}

.contact-btn {
  background: #1AAD19;
  color: white;
  border: none;
  border-radius: 30rpx;
  padding: 15rpx 30rpx;
  font-size: 24rpx;
}

/* 收货地址 */
.address-info {
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.address-content {
  margin-top: 20rpx;
}

.receiver-info {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-bottom: 15rpx;
}

.receiver-name {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
}

.receiver-phone {
  font-size: 28rpx;
  color: #666;
}

.address-detail {
  font-size: 28rpx;
  color: #666;
  line-height: 1.4;
}

/* 商品信息 */
.products-info {
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.product-list {
  margin-top: 20rpx;
}

.product-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.product-item:last-child {
  border-bottom: none;
}

.product-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
  object-fit: cover;
}

.product-info {
  flex: 1;
}

.product-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.product-spec {
  font-size: 24rpx;
  color: #666;
  background: #f0f0f0;
  padding: 6rpx 12rpx;
  border-radius: 6rpx;
  margin-bottom: 15rpx;
  display: inline-block;
}

.product-price-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.product-price {
  font-size: 28rpx;
  color: #e74c3c;
  font-weight: 600;
}

.product-quantity {
  font-size: 28rpx;
  color: #666;
}

/* 订单信息 */
.order-info {
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.order-details {
  margin-top: 20rpx;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  font-size: 28rpx;
  color: #666;
  width: 160rpx;
  flex-shrink: 0;
}

.detail-value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
  text-align: right;
  word-break: break-all;
}

/* 费用明细 */
.price-detail {
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.price-list {
  margin-top: 20rpx;
}

.price-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.price-item:last-child {
  border-bottom: none;
}

.price-item.total {
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
  margin-top: 10rpx;
}

.price-label {
  font-size: 28rpx;
  color: #666;
}

.price-value {
  font-size: 28rpx;
  color: #333;
}

.price-value.discount {
  color: #1AAD19;
}

.price-value.total-price {
  font-size: 32rpx;
  font-weight: 600;
  color: #e74c3c;
}

/* 底部操作栏 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 30rpx;
  border-top: 1rpx solid #eee;
  display: flex;
  gap: 20rpx;
  z-index: 100;
}

.action-btn {
  flex: 1;
  border: none;
  border-radius: 50rpx;
  padding: 25rpx;
  font-size: 28rpx;
}

.action-btn.cancel {
  background: #f8f8f8;
  color: #666;
}

.action-btn.pay {
  background: #1AAD19;
  color: white;
}

.action-btn.contact {
  background: #f8f8f8;
  color: #666;
}

.action-btn.confirm {
  background: #1AAD19;
  color: white;
}

.action-btn.review {
  background: #f8f8f8;
  color: #666;
}

.action-btn.rebuy {
  background: #1AAD19;
  color: white;
}
