// pages/index/index.js
const app = getApp()

Page({
  data: {
    banners: [
      {
        id: 1,
        image: 'https://via.placeholder.com/750x300/1AAD19/ffffff?text=山姆代购'
      },
      {
        id: 2,
        image: 'https://via.placeholder.com/750x300/FF6B6B/ffffff?text=新品上市'
      },
      {
        id: 3,
        image: 'https://via.placeholder.com/750x300/4ECDC4/ffffff?text=限时特价'
      }
    ],
    categories: [
      { id: 1, name: '生鲜食品', icon: 'https://via.placeholder.com/100x100/1AAD19/ffffff?text=生鲜' },
      { id: 2, name: '肉类海鲜', icon: 'https://via.placeholder.com/100x100/FF6B6B/ffffff?text=肉类' },
      { id: 3, name: '乳制品', icon: 'https://via.placeholder.com/100x100/4ECDC4/ffffff?text=乳品' },
      { id: 4, name: '零食饮料', icon: 'https://via.placeholder.com/100x100/45B7D1/ffffff?text=零食' },
      { id: 5, name: '日用百货', icon: 'https://via.placeholder.com/100x100/96CEB4/ffffff?text=百货' },
      { id: 6, name: '母婴用品', icon: 'https://via.placeholder.com/100x100/FFEAA7/ffffff?text=母婴' },
      { id: 7, name: '个护美妆', icon: 'https://via.placeholder.com/100x100/DDA0DD/ffffff?text=美妆' },
      { id: 8, name: '更多分类', icon: 'https://via.placeholder.com/100x100/F0F0F0/666666?text=更多' }
    ],
    specialProducts: [
      {
        id: 1,
        name: '澳洲牛肉',
        price: 89.9,
        originalPrice: 129.9,
        image: 'https://via.placeholder.com/200x200/FF6B6B/ffffff?text=牛肉',
        description: '优质澳洲进口牛肉，肉质鲜美'
      },
      {
        id: 2,
        name: '新西兰奇异果',
        price: 39.9,
        originalPrice: 59.9,
        image: 'https://via.placeholder.com/200x200/4ECDC4/ffffff?text=奇异果',
        description: '新鲜进口奇异果，营养丰富'
      },
      {
        id: 3,
        name: '有机蔬菜包',
        price: 29.9,
        originalPrice: 39.9,
        image: 'https://via.placeholder.com/200x200/1AAD19/ffffff?text=蔬菜',
        description: '新鲜有机蔬菜组合装'
      }
    ],
    hotProducts: [
      {
        id: 4,
        name: '挪威三文鱼',
        price: 168.0,
        image: 'https://via.placeholder.com/300x300/FF6B6B/ffffff?text=三文鱼',
        description: '新鲜挪威进口三文鱼，适合刺身'
      },
      {
        id: 5,
        name: '澳洲龙虾',
        price: 298.0,
        image: 'https://via.placeholder.com/300x300/4ECDC4/ffffff?text=龙虾',
        description: '鲜活澳洲龙虾，肉质Q弹'
      },
      {
        id: 6,
        name: '进口牛奶',
        price: 45.9,
        image: 'https://via.placeholder.com/300x300/45B7D1/ffffff?text=牛奶',
        description: '德国进口全脂牛奶，营养丰富'
      },
      {
        id: 7,
        name: '有机鸡蛋',
        price: 28.9,
        image: 'https://via.placeholder.com/300x300/FFEAA7/ffffff?text=鸡蛋',
        description: '农场直供有机鸡蛋，品质保证'
      }
    ],
    newProducts: [
      {
        id: 8,
        name: '日本和牛',
        price: 588.0,
        image: 'https://via.placeholder.com/300x300/FF6B6B/ffffff?text=和牛',
        description: '顶级日本和牛，入口即化'
      },
      {
        id: 9,
        name: '法国红酒',
        price: 299.0,
        image: 'https://via.placeholder.com/300x300/8E44AD/ffffff?text=红酒',
        description: '法国原装进口红酒，口感醇厚'
      }
    ]
  },

  onLoad() {
    this.loadData()
  },

  onShow() {
    // 页面显示时刷新数据
    this.setData({
      cartCount: app.globalData.cart.length
    })
  },

  onShareAppMessage() {
    return {
      title: '山姆代购 - 优质商品直达',
      path: '/pages/index/index'
    }
  },

  loadData() {
    // 这里可以调用云函数或API获取数据
    // 暂时使用模拟数据
    wx.showLoading({
      title: '加载中...'
    })

    setTimeout(() => {
      wx.hideLoading()
    }, 1000)
  },

  onSearchTap() {
    wx.navigateTo({
      url: '/pages/search/search'
    })
  },

  onCategoryTap(e) {
    const category = e.currentTarget.dataset.category
    wx.navigateTo({
      url: `/pages/category/category?categoryId=${category.id}&categoryName=${category.name}`
    })
  },

  onProductTap(e) {
    const product = e.currentTarget.dataset.product
    wx.navigateTo({
      url: `/pages/product-detail/product-detail?productId=${product.id}`
    })
  },

  onAddToCart(e) {
    const product = e.currentTarget.dataset.product
    app.addToCart(product)
    
    // 更新购物车数量显示
    this.setData({
      cartCount: app.globalData.cart.length
    })
  },

  onMoreTap(e) {
    const type = e.currentTarget.dataset.type
    wx.navigateTo({
      url: `/pages/product-list/product-list?type=${type}`
    })
  },

  onPullDownRefresh() {
    this.loadData()
    wx.stopPullDownRefresh()
  }
})
