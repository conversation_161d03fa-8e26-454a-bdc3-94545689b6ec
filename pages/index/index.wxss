/* pages/index/index.wxss */

/* 搜索栏 */
.search-bar {
  padding: 20rpx 0;
}

.search-input {
  background: white;
  border-radius: 50rpx;
  padding: 20rpx 30rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.search-icon {
  margin-right: 20rpx;
  color: #999;
}

.search-placeholder {
  color: #999;
  font-size: 28rpx;
}

/* 轮播图 */
.banner-swiper {
  height: 300rpx;
  border-radius: 16rpx;
  overflow: hidden;
  margin-bottom: 30rpx;
}

.banner-image {
  width: 100%;
  height: 100%;
}

/* 分类导航 */
.category-nav {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
  background: white;
  border-radius: 16rpx;
  padding: 30rpx 20rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 10rpx;
}

.category-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 16rpx;
  margin-bottom: 15rpx;
}

.category-name {
  font-size: 24rpx;
  color: #333;
  text-align: center;
}

/* 区块样式 */
.section {
  margin-bottom: 40rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding: 0 10rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.section-more {
  font-size: 28rpx;
  color: #1AAD19;
}

/* 横向滚动商品列表 */
.product-scroll {
  white-space: nowrap;
}

.product-list-horizontal {
  display: inline-flex;
  gap: 20rpx;
  padding: 0 10rpx;
}

.product-item-small {
  width: 200rpx;
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
}

.product-image-small {
  width: 100%;
  height: 150rpx;
  object-fit: cover;
}

.product-info-small {
  padding: 15rpx;
}

.product-title-small {
  font-size: 24rpx;
  color: #333;
  margin-bottom: 10rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-price-row {
  display: flex;
  align-items: center;
}

/* 商品网格 */
.product-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  padding: 0 10rpx;
}

.product-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 15rpx;
}

.add-cart-btn {
  width: 60rpx;
  height: 60rpx;
  background: #1AAD19;
  color: white;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  padding: 0;
  margin: 0;
}

.add-cart-btn::after {
  border: none;
}

.add-cart-btn:active {
  background: #0d8c13;
}

/* 响应式调整 */
@media (max-width: 400px) {
  .category-nav {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .category-icon {
    width: 60rpx;
    height: 60rpx;
  }
  
  .category-name {
    font-size: 22rpx;
  }
}
