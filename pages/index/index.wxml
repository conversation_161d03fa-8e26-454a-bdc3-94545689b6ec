<!--pages/index/index.wxml-->
<view class="container">
  <!-- 搜索栏 -->
  <view class="search-bar">
    <view class="search-input" bindtap="onSearchTap">
      <icon class="search-icon" type="search" size="16"></icon>
      <text class="search-placeholder">搜索山姆商品</text>
    </view>
  </view>

  <!-- 轮播图 -->
  <swiper class="banner-swiper" indicator-dots="true" autoplay="true" interval="3000" duration="500">
    <swiper-item wx:for="{{banners}}" wx:key="id">
      <image class="banner-image" src="{{item.image}}" mode="aspectFill"></image>
    </swiper-item>
  </swiper>

  <!-- 分类导航 -->
  <view class="category-nav">
    <view class="category-item" wx:for="{{categories}}" wx:key="id" bindtap="onCategoryTap" data-category="{{item}}">
      <image class="category-icon" src="{{item.icon}}" mode="aspectFit"></image>
      <text class="category-name">{{item.name}}</text>
    </view>
  </view>

  <!-- 今日特价 -->
  <view class="section">
    <view class="section-header">
      <text class="section-title">今日特价</text>
      <text class="section-more" bindtap="onMoreTap" data-type="special">查看更多 ></text>
    </view>
    <scroll-view class="product-scroll" scroll-x="true">
      <view class="product-list-horizontal">
        <view class="product-item-small" wx:for="{{specialProducts}}" wx:key="id" bindtap="onProductTap" data-product="{{item}}">
          <image class="product-image-small" src="{{item.image}}" mode="aspectFill"></image>
          <view class="product-info-small">
            <text class="product-title-small">{{item.name}}</text>
            <view class="product-price-row">
              <text class="product-price">¥{{item.price}}</text>
              <text class="product-original-price" wx:if="{{item.originalPrice}}">¥{{item.originalPrice}}</text>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 热门商品 -->
  <view class="section">
    <view class="section-header">
      <text class="section-title">热门商品</text>
      <text class="section-more" bindtap="onMoreTap" data-type="hot">查看更多 ></text>
    </view>
    <view class="product-grid">
      <view class="product-card" wx:for="{{hotProducts}}" wx:key="id" bindtap="onProductTap" data-product="{{item}}">
        <image class="product-image" src="{{item.image}}" mode="aspectFill"></image>
        <view class="product-info">
          <text class="product-title">{{item.name}}</text>
          <text class="product-desc">{{item.description}}</text>
          <view class="product-footer">
            <view class="product-price-row">
              <text class="product-price">¥{{item.price}}</text>
              <text class="product-original-price" wx:if="{{item.originalPrice}}">¥{{item.originalPrice}}</text>
            </view>
            <button class="add-cart-btn" bindtap="onAddToCart" data-product="{{item}}" catchtap="true">
              <icon type="plus" size="16"></icon>
            </button>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 新品推荐 -->
  <view class="section">
    <view class="section-header">
      <text class="section-title">新品推荐</text>
      <text class="section-more" bindtap="onMoreTap" data-type="new">查看更多 ></text>
    </view>
    <view class="product-grid">
      <view class="product-card" wx:for="{{newProducts}}" wx:key="id" bindtap="onProductTap" data-product="{{item}}">
        <image class="product-image" src="{{item.image}}" mode="aspectFill"></image>
        <view class="product-info">
          <text class="product-title">{{item.name}}</text>
          <text class="product-desc">{{item.description}}</text>
          <view class="product-footer">
            <view class="product-price-row">
              <text class="product-price">¥{{item.price}}</text>
              <text class="product-original-price" wx:if="{{item.originalPrice}}">¥{{item.originalPrice}}</text>
            </view>
            <button class="add-cart-btn" bindtap="onAddToCart" data-product="{{item}}" catchtap="true">
              <icon type="plus" size="16"></icon>
            </button>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>
