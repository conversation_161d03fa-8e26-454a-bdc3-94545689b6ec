/* pages/product-detail/product-detail.wxss */

.container {
  padding-bottom: 200rpx; /* 为底部操作栏留出空间 */
}

/* 商品图片轮播 */
.product-swiper {
  height: 750rpx;
  background: white;
}

.product-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 商品信息 */
.product-info {
  padding: 30rpx;
}

.product-header {
  margin-bottom: 20rpx;
}

.product-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.4;
  margin-bottom: 15rpx;
  display: block;
}

.product-tags {
  display: flex;
  gap: 10rpx;
  margin-bottom: 20rpx;
}

.tag {
  background: #f0f9ff;
  color: #1AAD19;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.product-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 30rpx;
  display: block;
}

/* 价格区域 */
.price-section {
  margin-bottom: 30rpx;
}

.price-row {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}

.current-price {
  font-size: 48rpx;
  font-weight: 600;
  color: #e74c3c;
  margin-right: 20rpx;
}

.original-price {
  font-size: 32rpx;
  color: #999;
  text-decoration: line-through;
  margin-right: 20rpx;
}

.discount {
  background: #e74c3c;
  color: white;
  padding: 6rpx 12rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
}

.price-note {
  display: flex;
  gap: 30rpx;
}

.service-fee,
.delivery-fee {
  font-size: 24rpx;
  color: #666;
}

/* 库存信息 */
.stock-info {
  display: flex;
  gap: 40rpx;
}

.stock-text,
.sales-text {
  font-size: 28rpx;
  color: #666;
}

/* 规格选择 */
.spec-section {
  padding: 30rpx;
}

.spec-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.spec-options {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
}

.spec-option {
  border: 2rpx solid #ddd;
  border-radius: 12rpx;
  padding: 20rpx 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 120rpx;
}

.spec-option.selected {
  border-color: #1AAD19;
  background: #f0f9ff;
}

.spec-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.spec-price {
  font-size: 24rpx;
  color: #e74c3c;
  font-weight: 600;
}

/* 详情区域 */
.detail-section {
  padding: 0;
}

.detail-tabs {
  display: flex;
  border-bottom: 1rpx solid #eee;
}

.tab {
  flex: 1;
  text-align: center;
  padding: 30rpx 20rpx;
  font-size: 32rpx;
  color: #666;
  border-bottom: 4rpx solid transparent;
}

.tab.active {
  color: #1AAD19;
  border-bottom-color: #1AAD19;
}

.tab-content {
  padding: 30rpx;
}

.detail-image {
  width: 100%;
  margin-bottom: 20rpx;
  border-radius: 8rpx;
}

.detail-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

/* 参数列表 */
.param-item {
  display: flex;
  justify-content: space-between;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.param-name {
  font-size: 28rpx;
  color: #666;
}

.param-value {
  font-size: 28rpx;
  color: #333;
}

/* 评价区域 */
.review-summary {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-bottom: 30rpx;
  padding: 20rpx;
  background: #f8f8f8;
  border-radius: 12rpx;
}

.review-score {
  font-size: 48rpx;
  font-weight: 600;
  color: #1AAD19;
}

.review-count {
  font-size: 28rpx;
  color: #666;
}

.review-item {
  border-bottom: 1rpx solid #f0f0f0;
  padding: 20rpx 0;
}

.review-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.reviewer-name {
  font-size: 28rpx;
  color: #333;
}

.review-stars {
  display: flex;
  gap: 5rpx;
}

.star {
  font-size: 24rpx;
  color: #ddd;
}

.star.filled {
  color: #ffd700;
}

.review-content {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 10rpx;
  display: block;
}

.review-time {
  font-size: 24rpx;
  color: #999;
}

/* 底部操作栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #eee;
  display: flex;
  align-items: center;
  gap: 30rpx;
  z-index: 100;
}

.quantity-section {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.quantity-label {
  font-size: 28rpx;
  color: #333;
}

.action-buttons {
  flex: 1;
  display: flex;
  gap: 20rpx;
}

.btn-cart {
  flex: 1;
  background: #f8f8f8;
  color: #333;
  border: none;
  border-radius: 50rpx;
  padding: 20rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10rpx;
}

.btn-buy {
  flex: 1;
  background: #1AAD19;
  color: white;
  border: none;
  border-radius: 50rpx;
  padding: 20rpx;
  font-size: 28rpx;
}
