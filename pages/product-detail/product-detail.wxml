<!--pages/product-detail/product-detail.wxml-->
<view class="container">
  <!-- 商品图片轮播 -->
  <swiper class="product-swiper" indicator-dots="true" indicator-color="rgba(255,255,255,0.5)" indicator-active-color="#1AAD19">
    <swiper-item wx:for="{{product.images}}" wx:key="*this">
      <image class="product-image" src="{{item}}" mode="aspectFill" bindtap="onImageTap" data-src="{{item}}"></image>
    </swiper-item>
  </swiper>

  <!-- 商品基本信息 -->
  <view class="product-info card">
    <view class="product-header">
      <text class="product-title">{{product.name}}</text>
      <view class="product-tags">
        <text class="tag" wx:for="{{product.tags}}" wx:key="*this">{{item}}</text>
      </view>
    </view>
    
    <text class="product-desc">{{product.description}}</text>
    
    <view class="price-section">
      <view class="price-row">
        <text class="current-price">¥{{product.price}}</text>
        <text class="original-price" wx:if="{{product.originalPrice}}">¥{{product.originalPrice}}</text>
        <text class="discount" wx:if="{{product.discount}}">{{product.discount}}折</text>
      </view>
      <view class="price-note">
        <text class="service-fee">服务费: ¥{{serviceFee}}</text>
        <text class="delivery-fee">配送费: ¥{{deliveryFee}}</text>
      </view>
    </view>

    <view class="stock-info">
      <text class="stock-text">库存: {{product.stock}}件</text>
      <text class="sales-text">已售: {{product.sales}}件</text>
    </view>
  </view>

  <!-- 规格选择 -->
  <view class="spec-section card" wx:if="{{product.specs && product.specs.length > 0}}">
    <view class="spec-title">选择规格</view>
    <view class="spec-options">
      <view class="spec-option {{selectedSpec === item.id ? 'selected' : ''}}" 
            wx:for="{{product.specs}}" 
            wx:key="id"
            bindtap="onSpecSelect" 
            data-spec="{{item}}">
        <text class="spec-name">{{item.name}}</text>
        <text class="spec-price">¥{{item.price}}</text>
      </view>
    </view>
  </view>

  <!-- 商品详情 -->
  <view class="detail-section card">
    <view class="detail-tabs">
      <view class="tab {{activeTab === 'detail' ? 'active' : ''}}" bindtap="onTabChange" data-tab="detail">
        商品详情
      </view>
      <view class="tab {{activeTab === 'params' ? 'active' : ''}}" bindtap="onTabChange" data-tab="params">
        商品参数
      </view>
      <view class="tab {{activeTab === 'reviews' ? 'active' : ''}}" bindtap="onTabChange" data-tab="reviews">
        用户评价
      </view>
    </view>

    <view class="tab-content">
      <!-- 商品详情 -->
      <view wx:if="{{activeTab === 'detail'}}" class="detail-content">
        <image wx:for="{{product.detailImages}}" wx:key="*this" src="{{item}}" mode="widthFix" class="detail-image"></image>
        <text class="detail-text">{{product.detailText}}</text>
      </view>

      <!-- 商品参数 -->
      <view wx:if="{{activeTab === 'params'}}" class="params-content">
        <view class="param-item" wx:for="{{product.params}}" wx:key="name">
          <text class="param-name">{{item.name}}</text>
          <text class="param-value">{{item.value}}</text>
        </view>
      </view>

      <!-- 用户评价 -->
      <view wx:if="{{activeTab === 'reviews'}}" class="reviews-content">
        <view class="review-summary">
          <text class="review-score">{{product.reviewScore}}</text>
          <text class="review-count">{{product.reviewCount}}条评价</text>
        </view>
        <view class="review-item" wx:for="{{product.reviews}}" wx:key="id">
          <view class="review-header">
            <text class="reviewer-name">{{item.userName}}</text>
            <view class="review-stars">
              <text class="star {{index < item.rating ? 'filled' : ''}}" wx:for="{{5}}" wx:key="*this">★</text>
            </view>
          </view>
          <text class="review-content">{{item.content}}</text>
          <text class="review-time">{{item.time}}</text>
        </view>
      </view>
    </view>
  </view>
</view>

<!-- 底部操作栏 -->
<view class="bottom-bar">
  <view class="quantity-section">
    <text class="quantity-label">数量</text>
    <view class="quantity-selector">
      <button class="quantity-btn" bindtap="onQuantityChange" data-type="minus">-</button>
      <input class="quantity-input" type="number" value="{{quantity}}" bindinput="onQuantityInput"></input>
      <button class="quantity-btn" bindtap="onQuantityChange" data-type="plus">+</button>
    </view>
  </view>
  
  <view class="action-buttons">
    <button class="btn-cart" bindtap="onAddToCart">
      <icon type="plus" size="16"></icon>
      加入购物车
    </button>
    <button class="btn-buy" bindtap="onBuyNow">立即购买</button>
  </view>
</view>
