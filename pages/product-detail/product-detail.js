// pages/product-detail/product-detail.js
const app = getApp()

Page({
  data: {
    productId: null,
    product: {},
    quantity: 1,
    selectedSpec: null,
    activeTab: 'detail',
    serviceFee: 0,
    deliveryFee: 10
  },

  onLoad(options) {
    const productId = options.productId
    this.setData({
      productId: productId
    })
    this.loadProductDetail(productId)
  },

  loadProductDetail(productId) {
    wx.showLoading({
      title: '加载中...'
    })

    // 模拟商品详情数据
    const mockProduct = {
      id: productId,
      name: '澳洲进口牛肉',
      description: '优质澳洲进口牛肉，肉质鲜美，营养丰富，适合煎炸炖煮',
      price: 89.9,
      originalPrice: 129.9,
      discount: 7,
      stock: 50,
      sales: 128,
      images: [
        'https://via.placeholder.com/750x750/FF6B6B/ffffff?text=牛肉1',
        'https://via.placeholder.com/750x750/4ECDC4/ffffff?text=牛肉2',
        'https://via.placeholder.com/750x750/45B7D1/ffffff?text=牛肉3'
      ],
      tags: ['进口', '新鲜', '优质'],
      specs: [
        { id: 1, name: '500g装', price: 89.9 },
        { id: 2, name: '1kg装', price: 169.9 },
        { id: 3, name: '2kg装', price: 319.9 }
      ],
      detailImages: [
        'https://via.placeholder.com/750x500/FF6B6B/ffffff?text=详情图1',
        'https://via.placeholder.com/750x500/4ECDC4/ffffff?text=详情图2'
      ],
      detailText: '澳洲进口优质牛肉，采用天然放牧方式饲养，肉质鲜美，口感嫩滑。富含蛋白质、铁质等营养成分，是家庭餐桌的理想选择。',
      params: [
        { name: '产地', value: '澳大利亚' },
        { name: '保质期', value: '冷冻保存30天' },
        { name: '储存方式', value: '-18°C冷冻保存' },
        { name: '包装规格', value: '500g/包' }
      ],
      reviewScore: 4.8,
      reviewCount: 156,
      reviews: [
        {
          id: 1,
          userName: '张***',
          rating: 5,
          content: '肉质很好，很新鲜，包装也很好',
          time: '2024-01-15'
        },
        {
          id: 2,
          userName: '李***',
          rating: 4,
          content: '味道不错，就是价格有点贵',
          time: '2024-01-14'
        }
      ]
    }

    setTimeout(() => {
      this.setData({
        product: mockProduct,
        selectedSpec: mockProduct.specs ? mockProduct.specs[0].id : null
      })
      this.calculateFees()
      wx.hideLoading()
    }, 1000)
  },

  calculateFees() {
    const currentPrice = this.getCurrentPrice()
    const serviceFee = (currentPrice * app.globalData.serviceRate).toFixed(2)
    this.setData({
      serviceFee: serviceFee,
      deliveryFee: app.globalData.deliveryFee
    })
  },

  getCurrentPrice() {
    const { product, selectedSpec } = this.data
    if (selectedSpec && product.specs) {
      const spec = product.specs.find(s => s.id === selectedSpec)
      return spec ? spec.price : product.price
    }
    return product.price
  },

  onImageTap(e) {
    const src = e.currentTarget.dataset.src
    wx.previewImage({
      current: src,
      urls: this.data.product.images
    })
  },

  onSpecSelect(e) {
    const spec = e.currentTarget.dataset.spec
    this.setData({
      selectedSpec: spec.id
    })
    this.calculateFees()
  },

  onTabChange(e) {
    const tab = e.currentTarget.dataset.tab
    this.setData({
      activeTab: tab
    })
  },

  onQuantityChange(e) {
    const type = e.currentTarget.dataset.type
    let { quantity } = this.data
    
    if (type === 'plus') {
      quantity += 1
    } else if (type === 'minus' && quantity > 1) {
      quantity -= 1
    }
    
    this.setData({ quantity })
  },

  onQuantityInput(e) {
    const value = parseInt(e.detail.value) || 1
    this.setData({
      quantity: Math.max(1, value)
    })
  },

  onAddToCart() {
    const { product, quantity, selectedSpec } = this.data
    const currentPrice = this.getCurrentPrice()
    
    const cartItem = {
      ...product,
      price: currentPrice,
      quantity: quantity,
      selectedSpec: selectedSpec
    }
    
    app.addToCart(cartItem)
  },

  onBuyNow() {
    const { product, quantity, selectedSpec } = this.data
    const currentPrice = this.getCurrentPrice()
    
    const orderItem = {
      ...product,
      price: currentPrice,
      quantity: quantity,
      selectedSpec: selectedSpec
    }
    
    wx.navigateTo({
      url: `/pages/order/order?type=buy&product=${encodeURIComponent(JSON.stringify(orderItem))}`
    })
  },

  onShareAppMessage() {
    return {
      title: this.data.product.name,
      path: `/pages/product-detail/product-detail?productId=${this.data.productId}`
    }
  }
})
