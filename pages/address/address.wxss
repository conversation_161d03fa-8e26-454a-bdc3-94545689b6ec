/* pages/address/address.wxss */

.container {
  padding-bottom: 120rpx; /* 为添加按钮留出空间 */
}

/* 地址列表 */
.address-list {
  padding: 20rpx;
}

.address-item {
  background: white;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  position: relative;
  display: flex;
  align-items: center;
}

.address-item.default {
  border: 2rpx solid #1AAD19;
}

.address-content {
  flex: 1;
  padding: 30rpx;
}

.address-header {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}

.receiver-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-right: 20rpx;
}

.receiver-phone {
  font-size: 28rpx;
  color: #666;
  margin-right: 20rpx;
}

.default-tag {
  background: #1AAD19;
  color: white;
  font-size: 20rpx;
  padding: 6rpx 12rpx;
  border-radius: 8rpx;
}

.address-detail {
  font-size: 28rpx;
  color: #666;
  line-height: 1.4;
  margin-bottom: 20rpx;
  display: block;
}

.address-actions {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  background: #f8f8f8;
  border: none;
  border-radius: 20rpx;
  padding: 12rpx 20rpx;
  font-size: 24rpx;
  color: #666;
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.action-btn::after {
  border: none;
}

.action-btn.edit {
  color: #1AAD19;
}

.action-btn.delete {
  color: #e74c3c;
}

.action-btn.default {
  color: #007aff;
}

.select-indicator {
  padding: 30rpx;
  border-left: 1rpx solid #f0f0f0;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 200rpx 40rpx;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 15rpx;
  display: block;
}

.empty-desc {
  font-size: 28rpx;
  color: #666;
}

/* 添加地址按钮 */
.add-address-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 30rpx;
  border-top: 1rpx solid #eee;
}

.add-address-btn {
  width: 100%;
  background: #1AAD19;
  color: white;
  border: none;
  border-radius: 50rpx;
  padding: 25rpx;
  font-size: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15rpx;
}

/* 地址编辑弹窗 */
.address-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.address-modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-radius: 32rpx 32rpx 0 0;
  max-height: 80vh;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.address-modal.show .modal-content {
  transform: translateY(0);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx 30rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.close-btn {
  color: #999;
}

.modal-body {
  padding: 30rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.form-group {
  margin-bottom: 40rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 15rpx;
  display: block;
}

.form-input {
  width: 100%;
  background: #f8f8f8;
  border-radius: 12rpx;
  padding: 25rpx 20rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
}

.form-textarea {
  width: 100%;
  min-height: 120rpx;
  background: #f8f8f8;
  border-radius: 12rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
}

.region-picker {
  background: #f8f8f8;
  border-radius: 12rpx;
  padding: 25rpx 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.region-text {
  font-size: 28rpx;
  color: #333;
}

.region-text.placeholder {
  color: #999;
}

.checkbox-group {
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.checkbox-label {
  font-size: 28rpx;
  color: #333;
}

.modal-footer {
  display: flex;
  gap: 20rpx;
  padding: 30rpx;
  border-top: 1rpx solid #f0f0f0;
}

.cancel-btn,
.confirm-btn {
  flex: 1;
  border: none;
  border-radius: 50rpx;
  padding: 25rpx;
  font-size: 32rpx;
}

.cancel-btn {
  background: #f8f8f8;
  color: #666;
}

.confirm-btn {
  background: #1AAD19;
  color: white;
}

/* 隐藏的地区选择器 */
.region-picker-hidden {
  position: absolute;
  left: -9999rpx;
  opacity: 0;
}
