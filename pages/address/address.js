// pages/address/address.js
Page({
  data: {
    mode: 'manage', // manage: 管理模式, select: 选择模式
    addresses: [],
    showModal: false,
    editingAddress: {
      id: null,
      name: '',
      phone: '',
      region: [],
      detail: '',
      isDefault: false
    }
  },

  onLoad(options) {
    const mode = options.mode || 'manage'
    this.setData({ mode })
    
    if (mode === 'select') {
      wx.setNavigationBarTitle({
        title: '选择地址'
      })
    } else {
      wx.setNavigationBarTitle({
        title: '地址管理'
      })
    }
    
    this.loadAddresses()
  },

  loadAddresses() {
    // 模拟地址数据
    const mockAddresses = [
      {
        id: 1,
        name: '张三',
        phone: '138****8888',
        region: ['北京市', '朝阳区', '三里屯街道'],
        detail: '工体北路8号院1号楼101室',
        fullAddress: '北京市朝阳区三里屯街道工体北路8号院1号楼101室',
        isDefault: true
      },
      {
        id: 2,
        name: '李四',
        phone: '139****9999',
        region: ['上海市', '浦东新区', '陆家嘴街道'],
        detail: '世纪大道100号环球金融中心20楼',
        fullAddress: '上海市浦东新区陆家嘴街道世纪大道100号环球金融中心20楼',
        isDefault: false
      }
    ]
    
    this.setData({
      addresses: mockAddresses
    })
  },

  onAddressSelect(e) {
    if (this.data.mode === 'select') {
      const address = e.currentTarget.dataset.address
      
      // 返回选中的地址
      const pages = getCurrentPages()
      const prevPage = pages[pages.length - 2]
      
      if (prevPage) {
        prevPage.setData({
          selectedAddress: address
        })
      }
      
      wx.navigateBack()
    }
  },

  onAddAddress() {
    this.setData({
      showModal: true,
      editingAddress: {
        id: null,
        name: '',
        phone: '',
        region: [],
        detail: '',
        isDefault: false
      }
    })
  },

  onEditAddress(e) {
    const address = e.currentTarget.dataset.address
    this.setData({
      showModal: true,
      editingAddress: {
        ...address
      }
    })
  },

  onDeleteAddress(e) {
    const addressId = e.currentTarget.dataset.id
    
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这个地址吗？',
      success: (res) => {
        if (res.confirm) {
          const addresses = this.data.addresses.filter(addr => addr.id !== addressId)
          this.setData({ addresses })
          
          wx.showToast({
            title: '删除成功',
            icon: 'success'
          })
        }
      }
    })
  },

  onSetDefault(e) {
    const addressId = e.currentTarget.dataset.id
    
    const addresses = this.data.addresses.map(addr => ({
      ...addr,
      isDefault: addr.id === addressId
    }))
    
    this.setData({ addresses })
    
    wx.showToast({
      title: '设置成功',
      icon: 'success'
    })
  },

  onCloseModal() {
    this.setData({ showModal: false })
  },

  onInputChange(e) {
    const field = e.currentTarget.dataset.field
    const value = e.detail.value
    
    this.setData({
      [`editingAddress.${field}`]: value
    })
  },

  onRegionChange() {
    // 触发地区选择器
    const picker = this.selectComponent('.region-picker-hidden')
    if (picker) {
      picker.tap()
    }
  },

  onRegionSelect(e) {
    const region = e.detail.value
    this.setData({
      'editingAddress.region': region
    })
  },

  onDefaultChange() {
    this.setData({
      'editingAddress.isDefault': !this.data.editingAddress.isDefault
    })
  },

  onSaveAddress() {
    const { editingAddress } = this.data
    
    // 验证表单
    if (!editingAddress.name.trim()) {
      wx.showToast({
        title: '请输入收货人姓名',
        icon: 'none'
      })
      return
    }
    
    if (!editingAddress.phone.trim()) {
      wx.showToast({
        title: '请输入手机号',
        icon: 'none'
      })
      return
    }
    
    if (!/^1[3-9]\d{9}$/.test(editingAddress.phone)) {
      wx.showToast({
        title: '请输入正确的手机号',
        icon: 'none'
      })
      return
    }
    
    if (editingAddress.region.length === 0) {
      wx.showToast({
        title: '请选择所在地区',
        icon: 'none'
      })
      return
    }
    
    if (!editingAddress.detail.trim()) {
      wx.showToast({
        title: '请输入详细地址',
        icon: 'none'
      })
      return
    }
    
    // 构建完整地址
    const fullAddress = editingAddress.region.join('') + editingAddress.detail
    
    let { addresses } = this.data
    
    if (editingAddress.id) {
      // 编辑现有地址
      addresses = addresses.map(addr => {
        if (addr.id === editingAddress.id) {
          return {
            ...editingAddress,
            fullAddress: fullAddress
          }
        }
        // 如果设置为默认，取消其他地址的默认状态
        if (editingAddress.isDefault) {
          return { ...addr, isDefault: false }
        }
        return addr
      })
    } else {
      // 添加新地址
      const newAddress = {
        ...editingAddress,
        id: Date.now(),
        fullAddress: fullAddress
      }
      
      // 如果设置为默认，取消其他地址的默认状态
      if (editingAddress.isDefault) {
        addresses = addresses.map(addr => ({ ...addr, isDefault: false }))
      }
      
      addresses.push(newAddress)
    }
    
    this.setData({
      addresses: addresses,
      showModal: false
    })
    
    wx.showToast({
      title: editingAddress.id ? '修改成功' : '添加成功',
      icon: 'success'
    })
  },

  preventTouchMove() {
    // 阻止弹窗背景滚动
    return false
  }
})
