<!--pages/address/address.wxml-->
<view class="container">
  <!-- 地址列表 -->
  <view class="address-list" wx:if="{{addresses.length > 0}}">
    <view class="address-item {{item.isDefault ? 'default' : ''}}" 
          wx:for="{{addresses}}" 
          wx:key="id"
          bindtap="onAddressSelect" 
          data-address="{{item}}">
      
      <view class="address-content">
        <view class="address-header">
          <text class="receiver-name">{{item.name}}</text>
          <text class="receiver-phone">{{item.phone}}</text>
          <view class="default-tag" wx:if="{{item.isDefault}}">默认</view>
        </view>
        
        <text class="address-detail">{{item.fullAddress}}</text>
        
        <view class="address-actions">
          <button class="action-btn edit" bindtap="onEditAddress" data-address="{{item}}" catchtap="true">
            <icon type="edit" size="14"></icon>
            编辑
          </button>
          <button class="action-btn delete" bindtap="onDeleteAddress" data-id="{{item.id}}" catchtap="true">
            <icon type="clear" size="14"></icon>
            删除
          </button>
          <button class="action-btn default" 
                  wx:if="{{!item.isDefault}}" 
                  bindtap="onSetDefault" 
                  data-id="{{item.id}}" 
                  catchtap="true">
            设为默认
          </button>
        </view>
      </view>
      
      <view class="select-indicator" wx:if="{{mode === 'select'}}">
        <icon type="success" size="20" color="#1AAD19"></icon>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{addresses.length === 0}}">
    <view class="empty-icon">📍</view>
    <text class="empty-text">还没有收货地址</text>
    <text class="empty-desc">添加收货地址，享受便捷配送服务</text>
  </view>

  <!-- 添加地址按钮 -->
  <view class="add-address-section">
    <button class="add-address-btn" bindtap="onAddAddress">
      <icon type="plus" size="16"></icon>
      添加新地址
    </button>
  </view>
</view>

<!-- 地址编辑弹窗 -->
<view class="address-modal {{showModal ? 'show' : ''}}" catchtouchmove="preventTouchMove">
  <view class="modal-mask" bindtap="onCloseModal"></view>
  <view class="modal-content">
    <view class="modal-header">
      <text class="modal-title">{{editingAddress.id ? '编辑地址' : '添加地址'}}</text>
      <icon class="close-btn" type="clear" size="20" bindtap="onCloseModal"></icon>
    </view>
    
    <view class="modal-body">
      <view class="form-group">
        <text class="form-label">收货人</text>
        <input class="form-input" 
               placeholder="请输入收货人姓名" 
               value="{{editingAddress.name}}"
               bindinput="onInputChange"
               data-field="name"></input>
      </view>
      
      <view class="form-group">
        <text class="form-label">手机号</text>
        <input class="form-input" 
               placeholder="请输入手机号" 
               type="number"
               value="{{editingAddress.phone}}"
               bindinput="onInputChange"
               data-field="phone"></input>
      </view>
      
      <view class="form-group">
        <text class="form-label">所在地区</text>
        <view class="region-picker" bindtap="onRegionChange">
          <text class="region-text {{editingAddress.region.length > 0 ? '' : 'placeholder'}}">
            {{editingAddress.region.length > 0 ? editingAddress.region.join(' ') : '请选择省市区'}}
          </text>
          <icon type="arrow" size="16" color="#ccc"></icon>
        </view>
      </view>
      
      <view class="form-group">
        <text class="form-label">详细地址</text>
        <textarea class="form-textarea" 
                  placeholder="请输入详细地址（街道、门牌号等）" 
                  value="{{editingAddress.detail}}"
                  bindinput="onInputChange"
                  data-field="detail"
                  maxlength="100"></textarea>
      </view>
      
      <view class="form-group checkbox-group">
        <checkbox checked="{{editingAddress.isDefault}}" bindtap="onDefaultChange"></checkbox>
        <text class="checkbox-label">设为默认地址</text>
      </view>
    </view>
    
    <view class="modal-footer">
      <button class="cancel-btn" bindtap="onCloseModal">取消</button>
      <button class="confirm-btn" bindtap="onSaveAddress">保存</button>
    </view>
  </view>
</view>

<!-- 地区选择器 -->
<picker mode="region" 
        value="{{editingAddress.region}}" 
        bindchange="onRegionSelect"
        class="region-picker-hidden">
</picker>
