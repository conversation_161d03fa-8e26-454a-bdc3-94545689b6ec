// pages/category/category.js
const app = getApp()

Page({
  data: {
    categories: [],
    activeCategory: 1,
    currentCategory: {},
    activeSubCategory: 0,
    products: [],
    sortType: 'default',
    priceOrder: 'desc',
    loading: false,
    hasMore: true,
    page: 1,
    pageSize: 20
  },

  onLoad(options) {
    const categoryId = parseInt(options.categoryId) || 1
    const categoryName = options.categoryName || ''
    
    this.setData({
      activeCategory: categoryId
    })
    
    if (categoryName) {
      wx.setNavigationBarTitle({
        title: categoryName
      })
    }
    
    this.loadCategories()
    this.loadProducts(true)
  },

  loadCategories() {
    // 模拟分类数据
    const categories = [
      {
        id: 1,
        name: '生鲜食品',
        count: 156,
        banner: 'https://via.placeholder.com/750x200/1AAD19/ffffff?text=生鲜食品',
        description: '新鲜优质，每日直供',
        subCategories: [
          { id: 11, name: '蔬菜' },
          { id: 12, name: '水果' },
          { id: 13, name: '菌菇' }
        ]
      },
      {
        id: 2,
        name: '肉类海鲜',
        count: 89,
        banner: 'https://via.placeholder.com/750x200/FF6B6B/ffffff?text=肉类海鲜',
        description: '优质肉类，新鲜海鲜',
        subCategories: [
          { id: 21, name: '猪肉' },
          { id: 22, name: '牛肉' },
          { id: 23, name: '海鲜' }
        ]
      },
      {
        id: 3,
        name: '乳制品',
        count: 67,
        banner: 'https://via.placeholder.com/750x200/4ECDC4/ffffff?text=乳制品',
        description: '营养丰富，品质保证',
        subCategories: [
          { id: 31, name: '牛奶' },
          { id: 32, name: '酸奶' },
          { id: 33, name: '奶酪' }
        ]
      },
      {
        id: 4,
        name: '零食饮料',
        count: 234,
        banner: 'https://via.placeholder.com/750x200/45B7D1/ffffff?text=零食饮料',
        description: '美味零食，清爽饮品',
        subCategories: [
          { id: 41, name: '饼干' },
          { id: 42, name: '饮料' },
          { id: 43, name: '坚果' }
        ]
      },
      {
        id: 5,
        name: '日用百货',
        count: 178,
        banner: 'https://via.placeholder.com/750x200/96CEB4/ffffff?text=日用百货',
        description: '生活必需，品质优选',
        subCategories: [
          { id: 51, name: '清洁用品' },
          { id: 52, name: '纸品' },
          { id: 53, name: '厨具' }
        ]
      },
      {
        id: 6,
        name: '母婴用品',
        count: 123,
        banner: 'https://via.placeholder.com/750x200/FFEAA7/ffffff?text=母婴用品',
        description: '安全放心，呵护成长',
        subCategories: [
          { id: 61, name: '奶粉' },
          { id: 62, name: '纸尿裤' },
          { id: 63, name: '玩具' }
        ]
      },
      {
        id: 7,
        name: '个护美妆',
        count: 145,
        banner: 'https://via.placeholder.com/750x200/DDA0DD/ffffff?text=个护美妆',
        description: '美丽护理，精致生活',
        subCategories: [
          { id: 71, name: '护肤' },
          { id: 72, name: '彩妆' },
          { id: 73, name: '洗护' }
        ]
      }
    ]
    
    const currentCategory = categories.find(cat => cat.id === this.data.activeCategory) || categories[0]
    
    this.setData({
      categories: categories,
      currentCategory: currentCategory
    })
  },

  loadProducts(reset = false) {
    if (this.data.loading) return
    
    this.setData({ loading: true })
    
    // 模拟商品数据
    const mockProducts = this.generateMockProducts()
    
    setTimeout(() => {
      if (reset) {
        this.setData({
          products: mockProducts,
          page: 1,
          hasMore: mockProducts.length >= this.data.pageSize,
          loading: false
        })
      } else {
        this.setData({
          products: [...this.data.products, ...mockProducts],
          page: this.data.page + 1,
          hasMore: mockProducts.length >= this.data.pageSize,
          loading: false
        })
      }
    }, 1000)
  },

  generateMockProducts() {
    const products = []
    const categoryNames = ['澳洲牛肉', '新西兰奇异果', '有机蔬菜', '进口牛奶', '挪威三文鱼']
    
    for (let i = 0; i < this.data.pageSize; i++) {
      const id = Date.now() + i
      products.push({
        id: id,
        name: categoryNames[i % categoryNames.length] + ` ${id}`,
        description: '优质商品，品质保证，新鲜直供',
        price: (Math.random() * 100 + 20).toFixed(1),
        originalPrice: (Math.random() * 50 + 100).toFixed(1),
        image: `https://via.placeholder.com/300x300/FF6B6B/ffffff?text=商品${id}`,
        sales: Math.floor(Math.random() * 1000),
        tags: ['新鲜', '优质', '进口'].slice(0, Math.floor(Math.random() * 3) + 1)
      })
    }
    
    return products
  },

  onCategorySelect(e) {
    const category = e.currentTarget.dataset.category
    
    this.setData({
      activeCategory: category.id,
      currentCategory: category,
      activeSubCategory: 0,
      page: 1
    })
    
    wx.setNavigationBarTitle({
      title: category.name
    })
    
    this.loadProducts(true)
  },

  onSubCategorySelect(e) {
    const subCategoryId = e.currentTarget.dataset.id
    
    this.setData({
      activeSubCategory: subCategoryId,
      page: 1
    })
    
    this.loadProducts(true)
  },

  onSortChange(e) {
    const sortType = e.currentTarget.dataset.type
    let { priceOrder } = this.data
    
    if (sortType === 'price') {
      priceOrder = priceOrder === 'asc' ? 'desc' : 'asc'
    }
    
    this.setData({
      sortType: sortType,
      priceOrder: priceOrder,
      page: 1
    })
    
    this.sortProducts(sortType, priceOrder)
  },

  sortProducts(sortType, priceOrder) {
    let { products } = this.data
    
    switch (sortType) {
      case 'sales':
        products.sort((a, b) => b.sales - a.sales)
        break
      case 'price':
        if (priceOrder === 'asc') {
          products.sort((a, b) => parseFloat(a.price) - parseFloat(b.price))
        } else {
          products.sort((a, b) => parseFloat(b.price) - parseFloat(a.price))
        }
        break
      default:
        // 综合排序，可以根据需要实现复杂的排序逻辑
        break
    }
    
    this.setData({ products })
  },

  onFilterTap() {
    wx.showToast({
      title: '筛选功能开发中',
      icon: 'none'
    })
  },

  onProductTap(e) {
    const product = e.currentTarget.dataset.product
    wx.navigateTo({
      url: `/pages/product-detail/product-detail?productId=${product.id}`
    })
  },

  onAddToCart(e) {
    const product = e.currentTarget.dataset.product
    app.addToCart(product)
  },

  onLoadMore() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadProducts()
    }
  },

  onPullDownRefresh() {
    this.loadProducts(true)
    wx.stopPullDownRefresh()
  }
})
