<!--pages/category/category.wxml-->
<view class="container">
  <view class="category-layout">
    <!-- 左侧分类菜单 -->
    <scroll-view class="category-sidebar" scroll-y="true">
      <view class="category-item {{activeCategory === item.id ? 'active' : ''}}" 
            wx:for="{{categories}}" 
            wx:key="id"
            bindtap="onCategorySelect" 
            data-category="{{item}}">
        <text class="category-name">{{item.name}}</text>
        <view class="category-badge" wx:if="{{item.count > 0}}">{{item.count}}</view>
      </view>
    </scroll-view>

    <!-- 右侧商品列表 -->
    <scroll-view class="product-content" scroll-y="true" bindscrolltolower="onLoadMore">
      <!-- 分类横幅 -->
      <view class="category-banner" wx:if="{{currentCategory.banner}}">
        <image class="banner-image" src="{{currentCategory.banner}}" mode="aspectFill"></image>
        <view class="banner-overlay">
          <text class="banner-title">{{currentCategory.name}}</text>
          <text class="banner-desc">{{currentCategory.description}}</text>
        </view>
      </view>

      <!-- 子分类筛选 -->
      <view class="sub-category-filter" wx:if="{{currentCategory.subCategories && currentCategory.subCategories.length > 0}}">
        <scroll-view class="sub-category-scroll" scroll-x="true">
          <view class="sub-category-list">
            <view class="sub-category-item {{activeSubCategory === 0 ? 'active' : ''}}" 
                  bindtap="onSubCategorySelect" 
                  data-id="0">
              全部
            </view>
            <view class="sub-category-item {{activeSubCategory === item.id ? 'active' : ''}}" 
                  wx:for="{{currentCategory.subCategories}}" 
                  wx:key="id"
                  bindtap="onSubCategorySelect" 
                  data-id="{{item.id}}">
              {{item.name}}
            </view>
          </view>
        </scroll-view>
      </view>

      <!-- 排序筛选 -->
      <view class="sort-filter">
        <view class="sort-item {{sortType === 'default' ? 'active' : ''}}" 
              bindtap="onSortChange" 
              data-type="default">
          综合
        </view>
        <view class="sort-item {{sortType === 'sales' ? 'active' : ''}}" 
              bindtap="onSortChange" 
              data-type="sales">
          销量
        </view>
        <view class="sort-item {{sortType === 'price' ? 'active' : ''}}" 
              bindtap="onSortChange" 
              data-type="price">
          价格
          <text class="sort-arrow {{priceOrder === 'asc' ? 'up' : 'down'}}">{{priceOrder === 'asc' ? '↑' : '↓'}}</text>
        </view>
        <view class="sort-item" bindtap="onFilterTap">
          筛选
          <icon type="search" size="14"></icon>
        </view>
      </view>

      <!-- 商品列表 -->
      <view class="product-list">
        <view class="product-item" wx:for="{{products}}" wx:key="id" bindtap="onProductTap" data-product="{{item}}">
          <image class="product-image" src="{{item.image}}" mode="aspectFill"></image>
          <view class="product-info">
            <text class="product-title">{{item.name}}</text>
            <text class="product-desc">{{item.description}}</text>
            <view class="product-tags" wx:if="{{item.tags && item.tags.length > 0}}">
              <text class="tag" wx:for="{{item.tags}}" wx:key="*this" wx:for-item="tag">{{tag}}</text>
            </view>
            <view class="product-footer">
              <view class="price-section">
                <text class="current-price">¥{{item.price}}</text>
                <text class="original-price" wx:if="{{item.originalPrice}}">¥{{item.originalPrice}}</text>
              </view>
              <view class="sales-info">
                <text class="sales-count">已售{{item.sales}}件</text>
              </view>
            </view>
            <button class="add-cart-btn" bindtap="onAddToCart" data-product="{{item}}" catchtap="true">
              <icon type="plus" size="16"></icon>
            </button>
          </view>
        </view>
      </view>

      <!-- 加载更多 -->
      <view class="load-more" wx:if="{{hasMore}}">
        <text class="load-text">{{loading ? '加载中...' : '上拉加载更多'}}</text>
      </view>

      <!-- 没有更多数据 -->
      <view class="no-more" wx:if="{{!hasMore && products.length > 0}}">
        <text class="no-more-text">没有更多商品了</text>
      </view>

      <!-- 空状态 -->
      <view class="empty-state" wx:if="{{products.length === 0 && !loading}}">
        <view class="empty-icon">📦</view>
        <text class="empty-text">暂无商品</text>
        <text class="empty-desc">该分类下暂时没有商品</text>
      </view>
    </scroll-view>
  </view>
</view>
