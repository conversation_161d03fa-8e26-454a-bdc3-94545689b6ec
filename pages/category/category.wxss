/* pages/category/category.wxss */

.container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.category-layout {
  flex: 1;
  display: flex;
  overflow: hidden;
}

/* 左侧分类菜单 */
.category-sidebar {
  width: 200rpx;
  background: #f8f8f8;
  border-right: 1rpx solid #eee;
}

.category-item {
  position: relative;
  padding: 30rpx 20rpx;
  border-bottom: 1rpx solid #eee;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.category-item.active {
  background: white;
  border-right: 4rpx solid #1AAD19;
}

.category-item.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 8rpx;
  background: #1AAD19;
}

.category-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.category-item.active .category-name {
  color: #1AAD19;
  font-weight: 600;
}

.category-badge {
  background: #e74c3c;
  color: white;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 10rpx;
  min-width: 30rpx;
  text-align: center;
}

/* 右侧内容区域 */
.product-content {
  flex: 1;
  background: #f8f8f8;
}

/* 分类横幅 */
.category-banner {
  position: relative;
  height: 200rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
}

.banner-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.banner-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0,0,0,0.6));
  padding: 30rpx;
  color: white;
}

.banner-title {
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 10rpx;
  display: block;
}

.banner-desc {
  font-size: 24rpx;
  opacity: 0.9;
}

/* 子分类筛选 */
.sub-category-filter {
  background: white;
  margin-bottom: 20rpx;
  padding: 20rpx 0;
}

.sub-category-scroll {
  white-space: nowrap;
}

.sub-category-list {
  display: inline-flex;
  gap: 20rpx;
  padding: 0 30rpx;
}

.sub-category-item {
  padding: 15rpx 30rpx;
  background: #f8f8f8;
  border-radius: 30rpx;
  font-size: 28rpx;
  color: #666;
  white-space: nowrap;
}

.sub-category-item.active {
  background: #1AAD19;
  color: white;
}

/* 排序筛选 */
.sort-filter {
  background: white;
  display: flex;
  padding: 20rpx 30rpx;
  margin-bottom: 20rpx;
  border-bottom: 1rpx solid #eee;
}

.sort-item {
  flex: 1;
  text-align: center;
  font-size: 28rpx;
  color: #666;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
}

.sort-item.active {
  color: #1AAD19;
}

.sort-arrow {
  font-size: 24rpx;
}

/* 商品列表 */
.product-list {
  padding: 0 20rpx;
}

.product-item {
  background: white;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  display: flex;
  position: relative;
}

.product-image {
  width: 200rpx;
  height: 200rpx;
  object-fit: cover;
  flex-shrink: 0;
}

.product-info {
  flex: 1;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
}

.product-title {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 10rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.product-desc {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 15rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-tags {
  display: flex;
  gap: 10rpx;
  margin-bottom: 15rpx;
}

.tag {
  background: #f0f9ff;
  color: #1AAD19;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
}

.product-footer {
  margin-top: auto;
}

.price-section {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.current-price {
  font-size: 32rpx;
  font-weight: 600;
  color: #e74c3c;
  margin-right: 15rpx;
}

.original-price {
  font-size: 24rpx;
  color: #999;
  text-decoration: line-through;
}

.sales-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sales-count {
  font-size: 24rpx;
  color: #666;
}

.add-cart-btn {
  position: absolute;
  bottom: 20rpx;
  right: 20rpx;
  width: 60rpx;
  height: 60rpx;
  background: #1AAD19;
  color: white;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  padding: 0;
  margin: 0;
}

.add-cart-btn::after {
  border: none;
}

.add-cart-btn:active {
  background: #0d8c13;
}

/* 加载状态 */
.load-more,
.no-more {
  text-align: center;
  padding: 40rpx;
}

.load-text,
.no-more-text {
  font-size: 28rpx;
  color: #666;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 100rpx 40rpx;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 15rpx;
  display: block;
}

.empty-desc {
  font-size: 28rpx;
  color: #666;
}
