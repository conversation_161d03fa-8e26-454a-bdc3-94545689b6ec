# 图标资源说明

本目录存放小程序所需的图标资源文件。

## 必需的图标文件

请在此目录下添加以下图标文件：

### TabBar 图标 (建议尺寸: 81x81px)
- `home.png` - 首页图标（未选中状态）
- `home-active.png` - 首页图标（选中状态）
- `category.png` - 分类图标（未选中状态）
- `category-active.png` - 分类图标（选中状态）
- `cart.png` - 购物车图标（未选中状态）
- `cart-active.png` - 购物车图标（选中状态）
- `profile.png` - 个人中心图标（未选中状态）
- `profile-active.png` - 个人中心图标（选中状态）

### 其他图标
- `default-avatar.png` - 默认用户头像 (建议尺寸: 200x200px)

## 图标设计规范

### 尺寸要求
- TabBar图标：81x81px（3倍图）
- 用户头像：200x200px
- 商品图片：建议最小300x300px

### 设计风格
- 简洁明了，符合小程序设计规范
- 使用统一的设计风格和色彩
- 确保在不同背景下都有良好的可视性

### 文件格式
- 推荐使用PNG格式
- 支持透明背景
- 文件大小尽量控制在合理范围内

## 临时解决方案

在开发阶段，如果没有设计好的图标，可以：

1. 使用微信小程序内置的icon组件
2. 使用在线图标生成工具
3. 使用占位图片服务（如项目中使用的placeholder.com）

## 获取图标资源

推荐的图标资源网站：
- [Iconfont](https://www.iconfont.cn/) - 阿里巴巴图标库
- [Feather Icons](https://feathericons.com/) - 简洁线性图标
- [Heroicons](https://heroicons.com/) - 现代SVG图标
- [Tabler Icons](https://tabler-icons.io/) - 免费SVG图标

## 注意事项

1. 确保图标符合微信小程序的设计规范
2. 注意图标的版权问题，使用免费或已授权的图标
3. 优化图标文件大小，避免影响小程序加载速度
4. 测试图标在不同设备和主题下的显示效果
