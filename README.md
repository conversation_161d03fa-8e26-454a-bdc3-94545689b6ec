# 山姆会员店代购小程序

一个专业的山姆会员店代购服务小程序，为用户提供便捷的商品选购和订单管理功能。

## 功能特色

### 🛍️ 商品展示
- 首页轮播图和分类导航
- 商品分类浏览和搜索
- 商品详情页面，支持规格选择
- 商品图片预览和详细介绍

### 🛒 购物体验
- 购物车管理，支持数量调整
- 商品收藏和浏览历史
- 智能推荐相关商品
- 优惠券和促销活动

### 📦 订单管理
- 完整的订单流程：下单→付款→代购→配送→完成
- 实时订单状态跟踪
- 物流信息查询
- 订单评价和售后服务

### 👤 用户中心
- 用户信息管理
- 收货地址管理
- 优惠券和积分系统
- 订单历史查询

### 🚚 代购服务
- 专业代购团队
- 实时价格同步
- 服务费透明计算
- 多种配送时间选择

## 技术架构

- **前端框架**: 微信小程序原生开发
- **UI设计**: 响应式设计，支持多种屏幕尺寸
- **数据管理**: 本地存储 + 云开发数据库
- **状态管理**: 全局状态管理（App.js）
- **组件化**: 模块化页面和组件设计

## 页面结构

```
pages/
├── index/           # 首页
├── category/        # 分类页面
├── product-detail/  # 商品详情
├── cart/           # 购物车
├── order/          # 订单确认
├── order-detail/   # 订单详情
├── profile/        # 个人中心
└── address/        # 地址管理
```

## 核心功能

### 商品管理
- 商品分类展示
- 商品搜索和筛选
- 商品详情和规格选择
- 购物车和收藏功能

### 订单流程
1. **商品选择** - 浏览商品，加入购物车
2. **订单确认** - 选择地址，确认商品信息
3. **在线支付** - 支持多种支付方式
4. **代购服务** - 专业团队代购采购
5. **物流配送** - 实时跟踪配送状态
6. **订单完成** - 确认收货，订单评价

### 用户服务
- 收货地址管理
- 订单历史查询
- 客服咨询服务
- 意见反馈处理

## 安装和使用

1. **下载代码**
   ```bash
   git clone [repository-url]
   cd shanmu-daigou
   ```

2. **配置小程序**
   - 在微信开发者工具中打开项目
   - 修改 `project.config.json` 中的 `appid`
   - 配置云开发环境ID（在 `app.js` 中）

3. **启动开发**
   - 使用微信开发者工具打开项目
   - 点击"编译"开始预览

## 配置说明

### 云开发配置
在 `app.js` 中配置云开发环境：
```javascript
wx.cloud.init({
  env: 'your-env-id', // 替换为您的云开发环境ID
  traceUser: true,
})
```

### 服务费配置
在 `app.js` 中可以调整服务费率：
```javascript
globalData: {
  serviceRate: 0.05, // 服务费率 5%
  deliveryFee: 10    // 基础配送费
}
```

## 开发规范

### 代码结构
- 使用模块化开发，每个页面独立管理
- 统一的样式规范和命名约定
- 响应式设计，适配不同屏幕尺寸

### 数据管理
- 使用全局状态管理购物车数据
- 本地存储用户偏好设置
- 云数据库存储订单和用户信息

### 用户体验
- 加载状态提示
- 错误处理和用户反馈
- 无障碍访问支持

## 部署上线

1. **代码审查** - 确保代码质量和安全性
2. **功能测试** - 完整测试所有功能模块
3. **性能优化** - 优化加载速度和用户体验
4. **提交审核** - 通过微信小程序平台提交审核
5. **发布上线** - 审核通过后正式发布

## 联系我们

如有任何问题或建议，请联系：
- 客服微信：sams_service
- 客服电话：400-123-4567
- 邮箱：<EMAIL>

## 许可证

本项目仅供学习和参考使用。
