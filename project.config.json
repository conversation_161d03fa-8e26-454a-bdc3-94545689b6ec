{"description": "山姆会员店代购小程序", "packOptions": {"ignore": [], "include": []}, "setting": {"bundle": false, "userConfirmedBundleSwitch": false, "urlCheck": true, "scopeDataCheck": false, "coverView": true, "es6": true, "postcss": true, "compileHotReLoad": false, "lazyloadPlaceholderEnable": false, "preloadBackgroundData": false, "minified": true, "autoAudits": false, "newFeature": false, "uglifyFileName": false, "uploadWithSourceMap": true, "useIsolateContext": true, "nodeModules": false, "enhance": true, "useMultiFrameRuntime": true, "useApiHook": true, "useApiHostProcess": true, "showShadowRootInWxmlPanel": true, "packNpmManually": false, "enableEngineNative": false, "packNpmRelationList": [], "minifyWXSS": true, "showES6CompileOption": false, "minifyWXML": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}}, "compileType": "miniprogram", "libVersion": "2.19.4", "appid": "your-app-id", "projectname": "shan<PERSON><PERSON><PERSON><PERSON><PERSON>", "condition": {}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}}